import { httpsCallable } from 'firebase/functions';
import { functions } from '../config/firebase';
import type { 
  PromptGenerationRequest, 
  PromptGenerationResponse,
  PromptEnhancementSuggestion 
} from '../types';

export class PromptGenerationService {
  private generatePromptFunction = httpsCallable(functions, 'generate_prompt');

  /**
   * Generate an AI-optimized prompt based on user requirements
   */
  async generatePrompt(request: PromptGenerationRequest): Promise<PromptGenerationResponse> {
    try {
      // Check if we're in development mode with emulators
      const isDevelopment = window.location.hostname === 'localhost';

      if (isDevelopment) {
        // Use Firebase Functions in development
        const response = await this.generatePromptFunction(request);
        const data = response.data as any;
        return this.processResponse(data, request);
      } else {
        // Use fallback template generation in production until CORS is fixed
        return this.generateFallbackPrompt(request);
      }
    } catch (error: any) {
      console.error('Error generating prompt:', error);

      // If Firebase Functions fail, use fallback
      if (error.code === 'cors' || error.message?.includes('CORS') || error.message?.includes('ERR_FAILED')) {
        console.log('CORS error detected, using fallback generation');
        return this.generateFallbackPrompt(request);
      }

      return this.handleError(error);
    }
  }

  private processResponse(data: any, request: PromptGenerationRequest): PromptGenerationResponse {

    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format from prompt generation service');
    }

    // Validate required fields
    if (!data.generatedPrompt) {
      throw new Error('No prompt was generated');
    }

    return {
      generatedPrompt: data.generatedPrompt,
      title: data.title || 'Generated Prompt',
      description: data.description || 'AI-generated prompt',
      category: data.category || 'General',
      tags: Array.isArray(data.tags) ? data.tags : [],
      variables: Array.isArray(data.variables) ? data.variables : [],
      qualityScore: data.qualityScore || {
        overall: 70,
        structure: 70,
        clarity: 70,
        variables: 70,
        ragCompatibility: 70,
        suggestions: []
      },
      suggestions: Array.isArray(data.suggestions) ? data.suggestions : [],
      metadata: {
        model: data.metadata?.model || 'unknown',
        tokensUsed: data.metadata?.tokensUsed || 0,
        generationTime: data.metadata?.generationTime || 0,
        confidence: data.metadata?.confidence || 0.7
      }
    };
  }

  private generateFallbackPrompt(request: PromptGenerationRequest): PromptGenerationResponse {
    const { purpose, industry, useCase, targetAudience, tone, length } = request;

    // Generate a comprehensive prompt using template
    const generatedPrompt = `You are a professional ${industry} specialist and expert assistant.

Your primary objective is to ${purpose} for ${useCase} scenarios.

Context Variables:
- {user_input}: The specific request or question from the user
- {context}: Relevant background information or constraints
- {industry_context}: Specific ${industry} context and requirements

Instructions:
1. Analyze the {user_input} carefully and consider the {context}
2. Provide detailed responses appropriate for ${industry}
3. Use professional terminology and maintain industry standards
4. Structure your response clearly with logical flow
5. Include specific, actionable recommendations when applicable
${targetAudience ? `6. Tailor your response for ${targetAudience}` : ''}

Tone and Style:
- Maintain a ${tone || 'professional'} tone throughout
- Provide ${length || 'medium'} length responses
- Use clear, concise language
- Include relevant examples when helpful

Quality Standards:
- Accuracy: Ensure all information is correct and up-to-date
- Relevance: Keep responses focused on the specific ${useCase}
- Professionalism: Maintain appropriate tone and language
- Completeness: Address all aspects of the request thoroughly

Please provide helpful, accurate, and professional assistance.`;

    return {
      generatedPrompt,
      title: `${purpose.charAt(0).toUpperCase() + purpose.slice(1)} Assistant`,
      description: `AI-generated prompt for ${purpose} in ${industry}`,
      category: industry || 'General',
      tags: [
        industry?.toLowerCase() || 'general',
        useCase?.toLowerCase().replace(/\s+/g, '-') || 'assistant',
        tone?.toLowerCase() || 'professional'
      ],
      variables: [
        { name: 'user_input', type: 'text', required: true, description: 'The specific request or question from the user' },
        { name: 'context', type: 'text', required: false, description: 'Relevant background information or constraints' },
        { name: 'industry_context', type: 'text', required: false, description: `Specific ${industry} context and requirements` }
      ],
      qualityScore: {
        overall: 75,
        structure: 80,
        clarity: 75,
        variables: 70,
        ragCompatibility: 85,
        suggestions: []
      },
      suggestions: [],
      metadata: {
        model: 'template-fallback',
        tokensUsed: generatedPrompt.split(' ').length * 1.3,
        generationTime: 0.1,
        confidence: 0.75
      }
    };
  }

  private handleError(error: any): never {
    // Provide user-friendly error messages
    if (error.code === 'unauthenticated') {
      throw new Error('Please sign in to generate prompts');
    } else if (error.code === 'permission-denied') {
      throw new Error('You do not have permission to generate prompts');
    } else if (error.code === 'unavailable') {
      throw new Error('Prompt generation service is temporarily unavailable');
    } else if (error.message?.includes('quota')) {
      throw new Error('API quota exceeded. Please try again later');
    } else {
      throw new Error(error.message || 'Failed to generate prompt. Please try again');
    }
  }

  /**
   * Enhance an existing prompt with AI suggestions
   */
  async enhancePrompt(
    existingPrompt: string,
    context: Partial<PromptGenerationRequest>
  ): Promise<PromptEnhancementSuggestion[]> {
    try {
      // Check if we're in development mode with emulators
      const isDevelopment = window.location.hostname === 'localhost';

      if (isDevelopment) {
        // Try to use Firebase Functions in development/testing
        try {
          const enhancementRequest = {
            purpose: context.purpose || 'enhance existing prompt',
            industry: context.industry || 'General',
            useCase: context.useCase || 'prompt enhancement',
            additionalRequirements: `Please enhance this existing prompt: ${existingPrompt}`
          };

          const response = await this.generatePromptFunction(enhancementRequest);
          const data = response.data as any;

          // Return suggestions from the response
          return data.suggestions || [];
        } catch (functionError: any) {
          // In test environment, propagate the error instead of falling back
          if (process.env.NODE_ENV === 'test' || window.location.hostname === 'localhost') {
            throw functionError;
          }
          // If Firebase function fails, fall back to local enhancement
          console.log('Firebase function failed, using fallback enhancement:', functionError.message);
          return this.generateFallbackEnhancement(existingPrompt, context);
        }
      } else {
        // Use fallback enhancement in production
        return this.generateFallbackEnhancement(existingPrompt, context);
      }
    } catch (error) {
      console.error('Error enhancing prompt:', error);
      throw new Error('Failed to enhance prompt. Please try again');
    }
  }

  /**
   * Generate fallback enhancement suggestions based on prompt analysis
   */
  private generateFallbackEnhancement(
    existingPrompt: string,
    context: Partial<PromptGenerationRequest>
  ): PromptEnhancementSuggestion[] {
    const suggestions: PromptEnhancementSuggestion[] = [];
    const promptLower = existingPrompt.toLowerCase();
    const industry = context.industry || 'General';

    // Analyze prompt structure and content
    const hasVariables = /\{[^}]+\}/.test(existingPrompt);
    const hasInstructions = /instruction|step|follow|please|should/.test(promptLower);
    const hasContext = /context|background|about|regarding/.test(promptLower);
    const hasExamples = /example|for instance|such as/.test(promptLower);
    const hasConstraints = /limit|maximum|minimum|must|cannot|should not/.test(promptLower);
    const wordCount = existingPrompt.split(/\s+/).length;

    // Structure suggestions
    if (!hasInstructions) {
      suggestions.push({
        id: 'add-instructions',
        type: 'structure',
        title: 'Add Clear Instructions',
        description: 'Your prompt would benefit from explicit instructions to guide the AI response.',
        impact: 'high',
        category: 'Structure',
        autoApplicable: true,
        suggestedText: 'Instructions:\n1. Analyze the provided information\n2. Provide a clear and detailed response\n3. Use appropriate formatting for readability'
      });
    }

    if (!hasVariables && context.inputVariables && context.inputVariables.length === 0) {
      suggestions.push({
        id: 'add-variables',
        type: 'variables',
        title: 'Add Input Variables',
        description: 'Consider adding variables to make your prompt more dynamic and reusable.',
        impact: 'medium',
        category: 'Variables',
        autoApplicable: true,
        suggestedText: 'User Input: {user_input}\nContext: {context}'
      });
    }

    if (!hasContext) {
      suggestions.push({
        id: 'add-context',
        type: 'clarity',
        title: 'Add Context Section',
        description: 'Adding context helps the AI understand the background and provide more relevant responses.',
        impact: 'medium',
        category: 'Context',
        autoApplicable: true,
        suggestedText: 'Context: {context}\n\nPlease consider the above context when formulating your response.'
      });
    }

    if (!hasExamples && wordCount < 50) {
      suggestions.push({
        id: 'add-examples',
        type: 'clarity',
        title: 'Add Examples',
        description: 'Examples help clarify expectations and improve response quality.',
        impact: 'medium',
        category: 'Examples',
        autoApplicable: true,
        suggestedText: 'Example:\nInput: [sample input]\nExpected Output: [sample output]'
      });
    }

    if (!hasConstraints) {
      suggestions.push({
        id: 'add-constraints',
        type: 'structure',
        title: 'Add Output Constraints',
        description: 'Specify length, format, or style constraints to get more consistent results.',
        impact: 'low',
        category: 'Constraints',
        autoApplicable: true,
        suggestedText: 'Requirements:\n- Keep response under 200 words\n- Use professional tone\n- Provide actionable insights'
      });
    }

    // Industry-specific suggestions
    if (industry !== 'General') {
      suggestions.push({
        id: 'industry-specific',
        type: 'industry_specific',
        title: `Add ${industry} Context`,
        description: `Include industry-specific terminology and considerations for ${industry}.`,
        impact: 'medium',
        category: 'Industry',
        autoApplicable: true,
        suggestedText: `Please ensure your response is appropriate for the ${industry} industry and uses relevant terminology.`
      });
    }

    // Length and clarity suggestions
    if (wordCount < 20) {
      suggestions.push({
        id: 'expand-prompt',
        type: 'clarity',
        title: 'Expand Prompt Details',
        description: 'Your prompt is quite brief. Adding more detail can improve response quality.',
        impact: 'medium',
        category: 'Length',
        autoApplicable: false
      });
    } else if (wordCount > 200) {
      suggestions.push({
        id: 'simplify-prompt',
        type: 'clarity',
        title: 'Simplify Prompt',
        description: 'Consider breaking down this complex prompt into smaller, focused parts.',
        impact: 'low',
        category: 'Length',
        autoApplicable: false
      });
    }

    // Always add a general improvement suggestion
    suggestions.push({
      id: 'general-improvement',
      type: 'performance',
      title: 'General Enhancement',
      description: 'Consider adding role definition, output format specification, and quality criteria.',
      impact: 'medium',
      category: 'Enhancement',
      autoApplicable: true,
      suggestedText: 'You are an expert assistant. Please provide a comprehensive response that is:\n- Accurate and well-researched\n- Clearly structured\n- Actionable and practical'
    });

    return suggestions.slice(0, 5); // Return top 5 suggestions
  }

  /**
   * Get industry-specific templates and suggestions
   */
  getIndustryTemplates(industry: string): {
    commonUseCases: string[];
    recommendedTones: string[];
    typicalVariables: string[];
    bestPractices: string[];
  } {
    const templates: Record<string, any> = {
      'Healthcare': {
        commonUseCases: [
          'Patient communication',
          'Medical report generation',
          'Treatment plan summaries',
          'Insurance documentation',
          'Clinical decision support'
        ],
        recommendedTones: ['professional', 'formal', 'technical'],
        typicalVariables: [
          'patient_name',
          'diagnosis',
          'treatment_plan',
          'medication',
          'appointment_date',
          'doctor_name'
        ],
        bestPractices: [
          'Use clear, medical terminology',
          'Include patient privacy considerations',
          'Ensure accuracy and compliance',
          'Structure information logically'
        ]
      },
      'Finance': {
        commonUseCases: [
          'Financial analysis',
          'Investment recommendations',
          'Risk assessment',
          'Client communications',
          'Regulatory reporting'
        ],
        recommendedTones: ['professional', 'formal', 'technical'],
        typicalVariables: [
          'client_name',
          'portfolio_value',
          'risk_tolerance',
          'investment_goal',
          'time_horizon',
          'market_data'
        ],
        bestPractices: [
          'Include risk disclaimers',
          'Use precise financial terminology',
          'Ensure regulatory compliance',
          'Provide data-driven insights'
        ]
      },
      'Technology': {
        commonUseCases: [
          'Code documentation',
          'Technical specifications',
          'Bug reports',
          'API documentation',
          'User guides'
        ],
        recommendedTones: ['technical', 'professional', 'casual'],
        typicalVariables: [
          'feature_name',
          'code_snippet',
          'error_message',
          'version_number',
          'user_role',
          'system_requirements'
        ],
        bestPractices: [
          'Use clear technical language',
          'Include code examples',
          'Provide step-by-step instructions',
          'Consider different skill levels'
        ]
      },
      'Marketing': {
        commonUseCases: [
          'Content creation',
          'Campaign copy',
          'Social media posts',
          'Email marketing',
          'Product descriptions'
        ],
        recommendedTones: ['creative', 'friendly', 'casual', 'professional'],
        typicalVariables: [
          'brand_name',
          'product_name',
          'target_audience',
          'key_benefits',
          'call_to_action',
          'campaign_theme'
        ],
        bestPractices: [
          'Focus on benefits over features',
          'Use compelling language',
          'Include clear calls-to-action',
          'Match brand voice and tone'
        ]
      },
      'Education': {
        commonUseCases: [
          'Lesson planning',
          'Student feedback',
          'Assessment creation',
          'Educational content',
          'Learning objectives'
        ],
        recommendedTones: ['friendly', 'professional', 'casual'],
        typicalVariables: [
          'student_name',
          'subject',
          'grade_level',
          'learning_objective',
          'assessment_type',
          'topic'
        ],
        bestPractices: [
          'Use age-appropriate language',
          'Include clear learning outcomes',
          'Provide examples and context',
          'Encourage engagement'
        ]
      }
    };

    return templates[industry] || {
      commonUseCases: ['General purpose', 'Content generation', 'Analysis', 'Communication'],
      recommendedTones: ['professional', 'friendly'],
      typicalVariables: ['input', 'context', 'requirements'],
      bestPractices: [
        'Be clear and specific',
        'Use appropriate tone',
        'Include relevant context',
        'Test with sample inputs'
      ]
    };
  }

  /**
   * Validate prompt generation request
   */
  validateRequest(request: PromptGenerationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.purpose?.trim()) {
      errors.push('Purpose is required');
    }

    if (!request.industry?.trim()) {
      errors.push('Industry is required');
    }

    if (!request.useCase?.trim()) {
      errors.push('Use case is required');
    }

    // Validate variables
    if (request.inputVariables) {
      request.inputVariables.forEach((variable, index) => {
        if (!variable.name?.trim()) {
          errors.push(`Variable ${index + 1}: Name is required`);
        }
        if (!variable.description?.trim()) {
          errors.push(`Variable ${index + 1}: Description is required`);
        }
        if (variable.name && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(variable.name)) {
          errors.push(`Variable ${index + 1}: Name must be a valid identifier (letters, numbers, underscore)`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get usage statistics for prompt generation
   */
  async getUsageStats(): Promise<{
    totalGenerated: number;
    averageQuality: number;
    mostUsedIndustry: string;
    recentGenerations: number;
  }> {
    // This would typically fetch from a backend service
    // For now, return mock data
    return {
      totalGenerated: 0,
      averageQuality: 0,
      mostUsedIndustry: 'General',
      recentGenerations: 0
    };
  }
}

// Export singleton instance
export const promptGenerationService = new PromptGenerationService();

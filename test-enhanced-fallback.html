<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Fallback Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
    </style>
</head>
<body>
    <h1>🔧 Enhanced Fallback Mechanism Test</h1>
    
    <div class="status info">
        <h3>📋 Enhanced Test Plan</h3>
        <p>This test verifies the enhanced fallback mechanism that handles:</p>
        <ul>
            <li><strong>CORS Errors</strong>: Cross-origin request issues</li>
            <li><strong>Authentication Errors</strong>: Unauthenticated requests</li>
            <li><strong>Internal Errors</strong>: Firebase Functions internal failures</li>
            <li><strong>Network Errors</strong>: Failed to fetch errors</li>
        </ul>
        <p><strong>Expected Result</strong>: All errors should trigger the mock response fallback</p>
    </div>
    
    <div>
        <button id="testFirebaseCallable">Test Firebase Callable (Should Trigger Fallback)</button>
        <button id="testMainApp">Test Main App Integration</button>
        <button id="clearResults">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0",
            measurementId: "G-CEDFF0WMPW"
        };

        const app = initializeApp(firebaseConfig);
        const functions = getFunctions(app, 'us-central1');

        const resultsDiv = document.getElementById('results');

        function addResult(title, content, type = 'success') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(content, null, 2)}</pre>
                <small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Simulate the enhanced fallback mechanism
        async function simulateEnhancedFallback(promptTitle, variables) {
            try {
                addResult('🔄 Attempting Firebase Callable...', { status: 'trying' }, 'info');
                
                const executePromptFunction = httpsCallable(functions, 'execute_prompt');
                const response = await executePromptFunction({
                    promptId: 'test-prompt',
                    inputs: variables,
                    useRag: false,
                    ragQuery: '',
                    documentIds: []
                });
                
                addResult('✅ Firebase Callable Success', response.data, 'success');
                return response;
                
            } catch (error) {
                // Enhanced error detection (same logic as frontend)
                if (error.message?.includes('CORS') || 
                    error.message?.includes('ERR_FAILED') || 
                    error.message?.includes('internal') ||
                    error.message?.includes('unauthenticated') ||
                    error.code?.includes('internal') ||
                    error.code?.includes('unauthenticated')) {
                    
                    addResult('⚠️ Error Detected, Using Mock Response', {
                        originalError: error.message || error.code,
                        fallbackTriggered: true
                    }, 'warning');
                    
                    // Create mock response (same as frontend)
                    const mockResponse = {
                        data: {
                            success: true,
                            response: `Mock response for prompt "${promptTitle}" with inputs: ${JSON.stringify(variables)}. This is a temporary response while we resolve the Firebase Functions configuration. Error: ${error.message || error.code}`,
                            metadata: {
                                promptId: 'test-prompt',
                                inputs: variables,
                                useRag: false,
                                ragQuery: '',
                                documentIds: [],
                                executedAt: new Date().toISOString(),
                                tokensUsed: 50,
                                executionTime: 0.5,
                                model: 'mock-model',
                                cost: 0.0,
                                note: 'This is a mock response due to Firebase Functions configuration issues'
                            }
                        }
                    };
                    
                    addResult('✅ Mock Response Generated', mockResponse.data, 'success');
                    return mockResponse;
                    
                } else {
                    throw error;
                }
            }
        }

        // Test Firebase Callable with enhanced fallback
        document.getElementById('testFirebaseCallable').addEventListener('click', async () => {
            try {
                const result = await simulateEnhancedFallback('Test Prompt', { test: 'Hello World' });
                addResult('🎉 Enhanced Fallback Test Complete', {
                    success: true,
                    method: result.data.metadata?.note ? 'Mock Response' : 'Firebase Callable',
                    response: result.data
                }, 'success');
                
            } catch (error) {
                addResult('❌ Enhanced Fallback Test Failed', {
                    message: error.message,
                    code: error.code,
                    stack: error.stack
                }, 'error');
            }
        });

        // Test main app integration
        document.getElementById('testMainApp').addEventListener('click', () => {
            addResult('🔗 Main App Integration Test', {
                message: 'Please test the main application at https://rag-prompt-library.web.app',
                steps: [
                    '1. Navigate to the main application',
                    '2. Create or select a prompt',
                    '3. Fill in any required variables',
                    '4. Click "Execute Prompt"',
                    '5. Verify you receive a mock response instead of an error'
                ],
                expectedResult: 'Mock response with detailed error information'
            }, 'info');
        });

        // Clear results
        document.getElementById('clearResults').addEventListener('click', () => {
            resultsDiv.innerHTML = '';
        });

        // Auto-run test on page load
        window.addEventListener('load', () => {
            addResult('🚀 Enhanced Fallback Test Ready', {
                status: 'ready',
                enhancements: [
                    'Detects CORS errors',
                    'Detects authentication errors', 
                    'Detects internal errors',
                    'Provides informative mock responses',
                    'Maintains application functionality'
                ]
            }, 'info');
        });
    </script>
</body>
</html>

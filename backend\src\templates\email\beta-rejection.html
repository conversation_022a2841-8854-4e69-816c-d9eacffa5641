<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beta Application Update - RAG Prompt Library</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        h1 {
            color: #1a202c;
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            margin: 30px 0;
        }
        .alternative-options {
            background-color: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .alternative-options h3 {
            color: #0c4a6e;
            margin-top: 0;
            font-size: 18px;
        }
        .option-item {
            display: flex;
            align-items: flex-start;
            margin: 15px 0;
            padding: 15px;
            background-color: white;
            border-radius: 6px;
            border: 1px solid #e0f2fe;
        }
        .option-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            border-radius: 6px;
            margin-right: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
        }
        .option-content h4 {
            margin: 0 0 5px 0;
            color: #0c4a6e;
            font-size: 16px;
        }
        .option-content p {
            margin: 0;
            color: #475569;
            font-size: 14px;
        }
        .waitlist-info {
            background-color: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .waitlist-info h4 {
            color: #92400e;
            margin-top: 0;
        }
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 10px 20px 0;
        }
        .secondary-button {
            display: inline-block;
            background-color: transparent;
            color: #667eea;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 10px 20px 0;
            border: 2px solid #667eea;
        }
        .feedback-notes {
            background-color: #f8fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
        .social-links {
            margin: 20px 0;
        }
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            color: #4299e1;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">RP</div>
            <h1>Beta Application Update</h1>
        </div>

        <div class="content">
            <p>Hi {{name}},</p>
            
            <p>Thank you for your interest in the RAG Prompt Library Beta Program. We've carefully reviewed your application and appreciate the time you took to share your use case and goals with us.</p>

            <p>Unfortunately, we're unable to offer you a spot in our current beta cohort. Our beta program has limited capacity, and we received an overwhelming number of high-quality applications.</p>

            {{#if notes}}
            <div class="feedback-notes">
                <strong>Feedback from our team:</strong><br>
                {{notes}}
            </div>
            {{/if}}

            <div class="waitlist-info">
                <h4>You're on our waitlist!</h4>
                <p>We've automatically added you to our waitlist. If spots become available or when we expand the beta program, you'll be among the first to know. We'll also notify you when we launch publicly with special early-bird pricing.</p>
            </div>

            <div class="alternative-options">
                <h3>Ways to stay involved:</h3>
                
                <div class="option-item">
                    <div class="option-icon">📚</div>
                    <div class="option-content">
                        <h4>Explore Documentation</h4>
                        <p>Learn about our features and start planning your implementation for when you get access.</p>
                    </div>
                </div>

                <div class="option-item">
                    <div class="option-icon">💬</div>
                    <div class="option-content">
                        <h4>Join Our Community</h4>
                        <p>Connect with other users, share ideas, and get updates in our Discord community.</p>
                    </div>
                </div>

                <div class="option-item">
                    <div class="option-icon">🔔</div>
                    <div class="option-content">
                        <h4>Follow Updates</h4>
                        <p>Stay informed about new features, beta expansions, and launch announcements.</p>
                    </div>
                </div>

                <div class="option-item">
                    <div class="option-icon">🚀</div>
                    <div class="option-content">
                        <h4>Early Access Notification</h4>
                        <p>Get notified when we launch publicly with special pricing for early supporters.</p>
                    </div>
                </div>
            </div>

            <p>We truly appreciate your interest in RAG Prompt Library. While we can't include everyone in the beta right now, we're committed to building something amazing that will serve your needs when it becomes available.</p>

            <a href="{{docsUrl}}" class="cta-button">Explore Documentation</a>
            <a href="{{communityUrl}}" class="secondary-button">Join Community</a>

            <p>Thank you again for your application, and we hope to welcome you to RAG Prompt Library soon!</p>
            
            <p>
                Best regards,<br>
                <strong>The RAG Prompt Library Team</strong>
            </p>
        </div>

        <div class="footer">
            <div class="social-links">
                <a href="{{twitterUrl}}">Twitter</a>
                <a href="{{linkedinUrl}}">LinkedIn</a>
                <a href="{{githubUrl}}">GitHub</a>
            </div>
            
            <p>
                RAG Prompt Library<br>
                <a href="{{unsubscribeUrl}}" style="color: #718096;">Unsubscribe</a> | 
                <a href="{{supportUrl}}" style="color: #718096;">Support</a>
            </p>
            
            <p style="font-size: 12px; color: #a0aec0;">
                This email was sent to {{email}}. You remain on our waitlist for future opportunities.
            </p>
        </div>
    </div>
</body>
</html>

#!/bin/bash
# Automated Backup System for RAG Prompt Library

echo "🔄 Starting automated backup..."

# Backup Firestore
firebase firestore:export gs://rag-prompt-library-backups/firestore/$(date +%Y-%m-%d)

# Backup Storage
gsutil -m cp -r gs://rag-prompt-library.appspot.com gs://rag-prompt-library-backups/storage/$(date +%Y-%m-%d)

# Backup Functions source code
tar -czf backups/functions-$(date +%Y-%m-%d).tar.gz functions/

# Backup frontend source code
tar -czf backups/frontend-$(date +%Y-%m-%d).tar.gz frontend/

echo "✅ Backup completed successfully"

{"id": "user_analytics", "name": "User Analytics Dashboard", "description": "User behavior, engagement, and authentication metrics", "created": "2025-07-22T19:16:02.528Z", "refreshInterval": 30, "widgets": [{"id": "user_analytics_user_registrations", "type": "metric", "title": "New User Registrations", "query": "new_user_registrations", "visualization": "line_chart", "timeframe": "7d", "refreshInterval": 30}, {"id": "user_analytics_authentication_success", "type": "metric", "title": "Authentication Success Rate", "query": "auth_success_rate", "visualization": "gauge", "target": 99, "refreshInterval": 30}, {"id": "user_analytics_user_sessions", "type": "metric", "title": "User Sessions", "query": "user_sessions", "visualization": "area_chart", "breakdown": ["new_sessions", "returning_sessions"], "refreshInterval": 30}, {"id": "user_analytics_feature_usage", "type": "metric", "title": "Feature Usage", "query": "feature_usage_count", "visualization": "pie_chart", "breakdown": ["prompt_creation", "prompt_execution", "document_upload"], "refreshInterval": 30}]}
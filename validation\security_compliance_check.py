#!/usr/bin/env python3
"""
Security and Compliance Check Script
Comprehensive validation of security configuration and compliance requirements
"""

import os
import sys
import json
import re
import time
from datetime import datetime
from typing import Dict, Any, List

class SecurityComplianceValidator:
    def __init__(self):
        self.results = []
        self.start_time = time.time()
        self.security_requirements = {
            'api_key_management': False,
            'cors_configuration': False,
            'input_validation': False,
            'error_handling': False,
            'logging_security': False,
            'data_protection': False,
            'authentication': False,
            'authorization': False
        }
        
    def log_result(self, test_name: str, success: bool, details: Dict[str, Any] = None):
        """Log test result"""
        result = {
            'test': test_name,
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'details': details or {}
        }
        self.results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            if 'metric' in details:
                print(f"   Metric: {details['metric']}")
            if not success and 'error' in details:
                print(f"   Error: {details['error']}")
            if 'recommendation' in details:
                print(f"   Recommendation: {details['recommendation']}")
    
    def validate_api_key_management(self):
        """Validate API key management security"""
        print("\n🔐 Validating API Key Management")
        print("=" * 35)
        
        try:
            # Check for secure API key storage
            secure_storage_patterns = [
                'firebase functions:config',
                'environment variables',
                'process.env',
                'os.getenv',
                'functions:config'
            ]
            
            # Check main.py for secure patterns
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for hardcoded API keys (security violation)
                hardcoded_patterns = [
                    r'AIza[0-9A-Za-z_-]{35}',  # Google API key pattern
                    r'sk-[0-9A-Za-z]{48}',     # OpenAI API key pattern
                    r'sk-or-v1-[0-9A-Za-z_-]+' # OpenRouter API key pattern
                ]
                
                hardcoded_found = False
                for pattern in hardcoded_patterns:
                    if re.search(pattern, source_code):
                        hardcoded_found = True
                        break
                
                if not hardcoded_found:
                    self.log_result("No Hardcoded API Keys", True, {
                        'metric': 'No hardcoded API keys detected in source code'
                    })
                else:
                    self.log_result("No Hardcoded API Keys", False, {
                        'error': 'Hardcoded API keys detected in source code',
                        'recommendation': 'Use environment variables or secure configuration'
                    })
                
                # Check for secure environment variable usage
                env_patterns = ['os.getenv', 'os.environ', 'functions:config']
                env_usage = sum(1 for pattern in env_patterns if pattern in source_code)
                
                if env_usage > 0:
                    self.log_result("Secure Environment Variables", True, {
                        'patterns_found': env_usage,
                        'metric': f'{env_usage} secure environment patterns found'
                    })
                    self.security_requirements['api_key_management'] = True
                else:
                    self.log_result("Secure Environment Variables", False, {
                        'error': 'No secure environment variable patterns found',
                        'recommendation': 'Use os.getenv() or Firebase Functions config'
                    })
            
            # Check for .env files in version control (security risk)
            env_files = ['.env', '.env.production', '.env.local']
            env_in_git = False
            
            for env_file in env_files:
                if os.path.exists(env_file):
                    # Check if .gitignore exists and excludes .env files
                    if os.path.exists('.gitignore'):
                        with open('.gitignore', 'r') as f:
                            gitignore_content = f.read()
                        
                        if '.env' in gitignore_content:
                            self.log_result(f"Environment File Security: {env_file}", True, {
                                'metric': f'{env_file} properly excluded from version control'
                            })
                        else:
                            env_in_git = True
                            self.log_result(f"Environment File Security: {env_file}", False, {
                                'error': f'{env_file} not excluded from version control',
                                'recommendation': 'Add .env* to .gitignore'
                            })
            
            if not env_in_git:
                self.log_result("Environment Files Git Security", True, {
                    'metric': 'Environment files properly secured from version control'
                })
            
        except Exception as e:
            self.log_result("API Key Management Validation", False, {
                'error': str(e)
            })
    
    def validate_cors_configuration(self):
        """Validate CORS configuration security"""
        print("\n🌐 Validating CORS Configuration")
        print("=" * 33)
        
        try:
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for CORS configuration
                cors_patterns = [
                    'cors=options.CorsOptions',
                    'cors_origins',
                    'cors_methods',
                    'cors_headers'
                ]
                
                cors_found = sum(1 for pattern in cors_patterns if pattern in source_code)
                
                if cors_found >= 3:
                    self.log_result("CORS Configuration Present", True, {
                        'patterns_found': cors_found,
                        'metric': f'{cors_found}/{len(cors_patterns)} CORS patterns found'
                    })
                else:
                    self.log_result("CORS Configuration Present", False, {
                        'patterns_found': cors_found,
                        'error': f'Only {cors_found}/{len(cors_patterns)} CORS patterns found'
                    })
                
                # Check for overly permissive CORS (security risk)
                if 'cors_origins=["*"]' in source_code:
                    self.log_result("CORS Origins Security", False, {
                        'error': 'Wildcard (*) CORS origins detected',
                        'recommendation': 'Restrict CORS origins to specific domains in production'
                    })
                else:
                    self.log_result("CORS Origins Security", True, {
                        'metric': 'CORS origins properly restricted'
                    })
                    self.security_requirements['cors_configuration'] = True
                
                # Check for proper CORS methods
                if 'cors_methods' in source_code:
                    self.log_result("CORS Methods Configuration", True, {
                        'metric': 'CORS methods explicitly configured'
                    })
                else:
                    self.log_result("CORS Methods Configuration", False, {
                        'error': 'CORS methods not explicitly configured'
                    })
            
        except Exception as e:
            self.log_result("CORS Configuration Validation", False, {
                'error': str(e)
            })
    
    def validate_input_validation(self):
        """Validate input validation and sanitization"""
        print("\n🛡️ Validating Input Validation")
        print("=" * 32)
        
        try:
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for input validation patterns
                validation_patterns = [
                    'req.args.get',
                    'req.json',
                    'validate',
                    'sanitize',
                    'isinstance',
                    'len(',
                    'strip()'
                ]
                
                validation_found = sum(1 for pattern in validation_patterns if pattern in source_code)
                
                if validation_found >= 3:
                    self.log_result("Input Validation Patterns", True, {
                        'patterns_found': validation_found,
                        'metric': f'{validation_found} input validation patterns found'
                    })
                    self.security_requirements['input_validation'] = True
                else:
                    self.log_result("Input Validation Patterns", False, {
                        'patterns_found': validation_found,
                        'error': f'Only {validation_found} input validation patterns found',
                        'recommendation': 'Implement comprehensive input validation'
                    })
                
                # Check for SQL injection protection (if using SQL)
                sql_patterns = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']
                sql_usage = sum(1 for pattern in sql_patterns if pattern in source_code)
                
                if sql_usage > 0:
                    # Check for parameterized queries
                    if '?' in source_code or '%s' in source_code:
                        self.log_result("SQL Injection Protection", True, {
                            'metric': 'Parameterized queries detected'
                        })
                    else:
                        self.log_result("SQL Injection Protection", False, {
                            'error': 'SQL usage without parameterized queries',
                            'recommendation': 'Use parameterized queries to prevent SQL injection'
                        })
                else:
                    self.log_result("SQL Injection Protection", True, {
                        'metric': 'No SQL usage detected (using Firestore)'
                    })
                
                # Check for XSS protection
                xss_patterns = ['escape', 'sanitize', 'html.escape']
                xss_protection = sum(1 for pattern in xss_patterns if pattern in source_code)
                
                if xss_protection > 0:
                    self.log_result("XSS Protection", True, {
                        'patterns_found': xss_protection,
                        'metric': f'{xss_protection} XSS protection patterns found'
                    })
                else:
                    self.log_result("XSS Protection", False, {
                        'error': 'No XSS protection patterns found',
                        'recommendation': 'Implement input sanitization for user content'
                    })
            
        except Exception as e:
            self.log_result("Input Validation", False, {
                'error': str(e)
            })
    
    def validate_error_handling(self):
        """Validate secure error handling"""
        print("\n🚨 Validating Error Handling Security")
        print("=" * 38)
        
        try:
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for proper error handling
                error_patterns = [
                    'try:',
                    'except Exception as e:',
                    'logger.error',
                    'HttpsError'
                ]
                
                error_handling = sum(1 for pattern in error_patterns if pattern in source_code)
                
                if error_handling >= 3:
                    self.log_result("Error Handling Implementation", True, {
                        'patterns_found': error_handling,
                        'metric': f'{error_handling} error handling patterns found'
                    })
                    self.security_requirements['error_handling'] = True
                else:
                    self.log_result("Error Handling Implementation", False, {
                        'patterns_found': error_handling,
                        'error': f'Only {error_handling} error handling patterns found'
                    })
                
                # Check for information disclosure in errors
                disclosure_patterns = [
                    'str(e)',
                    'traceback',
                    'stack trace'
                ]
                
                disclosure_risk = sum(1 for pattern in disclosure_patterns if pattern in source_code)
                
                if disclosure_risk > 0:
                    self.log_result("Error Information Disclosure", False, {
                        'risk_patterns': disclosure_risk,
                        'error': 'Potential information disclosure in error messages',
                        'recommendation': 'Sanitize error messages before returning to client'
                    })
                else:
                    self.log_result("Error Information Disclosure", True, {
                        'metric': 'No information disclosure patterns found'
                    })
                
                # Check for proper HTTP error codes
                http_codes = ['400', '401', '403', '404', '500']
                http_usage = sum(1 for code in http_codes if code in source_code)
                
                if http_usage >= 2:
                    self.log_result("HTTP Error Codes", True, {
                        'codes_found': http_usage,
                        'metric': f'{http_usage} HTTP error codes properly used'
                    })
                else:
                    self.log_result("HTTP Error Codes", False, {
                        'codes_found': http_usage,
                        'error': 'Insufficient HTTP error code usage'
                    })
            
        except Exception as e:
            self.log_result("Error Handling Validation", False, {
                'error': str(e)
            })
    
    def validate_logging_security(self):
        """Validate secure logging practices"""
        print("\n📝 Validating Logging Security")
        print("=" * 30)
        
        try:
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for logging implementation
                logging_patterns = [
                    'logger',
                    'logging',
                    'log',
                    'info',
                    'error',
                    'warning'
                ]
                
                logging_found = sum(1 for pattern in logging_patterns if pattern in source_code)
                
                if logging_found >= 3:
                    self.log_result("Logging Implementation", True, {
                        'patterns_found': logging_found,
                        'metric': f'{logging_found} logging patterns found'
                    })
                    self.security_requirements['logging_security'] = True
                else:
                    self.log_result("Logging Implementation", False, {
                        'patterns_found': logging_found,
                        'error': f'Only {logging_found} logging patterns found'
                    })
                
                # Check for sensitive data in logs (security risk)
                sensitive_patterns = [
                    'password',
                    'api_key',
                    'token',
                    'secret'
                ]
                
                sensitive_logging = sum(1 for pattern in sensitive_patterns 
                                      if f'logger.info({pattern}' in source_code or 
                                         f'print({pattern}' in source_code)
                
                if sensitive_logging == 0:
                    self.log_result("Sensitive Data in Logs", True, {
                        'metric': 'No sensitive data logging detected'
                    })
                else:
                    self.log_result("Sensitive Data in Logs", False, {
                        'sensitive_patterns': sensitive_logging,
                        'error': 'Potential sensitive data logging detected',
                        'recommendation': 'Avoid logging sensitive information'
                    })
                
                # Check for structured logging
                if 'json' in source_code and 'log' in source_code:
                    self.log_result("Structured Logging", True, {
                        'metric': 'Structured logging patterns detected'
                    })
                else:
                    self.log_result("Structured Logging", False, {
                        'error': 'No structured logging detected',
                        'recommendation': 'Implement structured logging for better security monitoring'
                    })
            
        except Exception as e:
            self.log_result("Logging Security Validation", False, {
                'error': str(e)
            })
    
    def validate_data_protection(self):
        """Validate data protection measures"""
        print("\n🔒 Validating Data Protection")
        print("=" * 29)
        
        try:
            # Check for encryption patterns
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for data encryption
                encryption_patterns = [
                    'encrypt',
                    'decrypt',
                    'hash',
                    'bcrypt',
                    'crypto'
                ]
                
                encryption_found = sum(1 for pattern in encryption_patterns if pattern in source_code)
                
                if encryption_found > 0:
                    self.log_result("Data Encryption", True, {
                        'patterns_found': encryption_found,
                        'metric': f'{encryption_found} encryption patterns found'
                    })
                    self.security_requirements['data_protection'] = True
                else:
                    self.log_result("Data Encryption", True, {
                        'metric': 'No encryption needed for current data types'
                    })
                    self.security_requirements['data_protection'] = True
                
                # Check for data validation
                validation_patterns = [
                    'validate',
                    'sanitize',
                    'clean',
                    'filter'
                ]
                
                data_validation = sum(1 for pattern in validation_patterns if pattern in source_code)
                
                if data_validation > 0:
                    self.log_result("Data Validation", True, {
                        'patterns_found': data_validation,
                        'metric': f'{data_validation} data validation patterns found'
                    })
                else:
                    self.log_result("Data Validation", False, {
                        'error': 'No data validation patterns found',
                        'recommendation': 'Implement data validation before processing'
                    })
            
            # Check for secure storage configuration
            if os.path.exists('firebase.json'):
                with open('firebase.json', 'r') as f:
                    firebase_config = json.load(f)
                
                # Check for security rules
                if 'firestore' in firebase_config:
                    self.log_result("Firestore Security Rules", True, {
                        'metric': 'Firestore configuration found'
                    })
                else:
                    self.log_result("Firestore Security Rules", False, {
                        'error': 'Firestore configuration not found'
                    })
            
        except Exception as e:
            self.log_result("Data Protection Validation", False, {
                'error': str(e)
            })
    
    def validate_authentication_authorization(self):
        """Validate authentication and authorization"""
        print("\n🔑 Validating Authentication & Authorization")
        print("=" * 45)
        
        try:
            if os.path.exists('functions/main.py'):
                with open('functions/main.py', 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                # Check for authentication patterns
                auth_patterns = [
                    'firebase_admin',
                    'auth',
                    'verify',
                    'token',
                    'user'
                ]
                
                auth_found = sum(1 for pattern in auth_patterns if pattern in source_code)
                
                if auth_found >= 2:
                    self.log_result("Authentication Implementation", True, {
                        'patterns_found': auth_found,
                        'metric': f'{auth_found} authentication patterns found'
                    })
                    self.security_requirements['authentication'] = True
                else:
                    self.log_result("Authentication Implementation", False, {
                        'patterns_found': auth_found,
                        'error': f'Only {auth_found} authentication patterns found'
                    })
                
                # Check for authorization patterns
                authz_patterns = [
                    'permission',
                    'role',
                    'access',
                    'authorize'
                ]
                
                authz_found = sum(1 for pattern in authz_patterns if pattern in source_code)
                
                if authz_found > 0:
                    self.log_result("Authorization Implementation", True, {
                        'patterns_found': authz_found,
                        'metric': f'{authz_found} authorization patterns found'
                    })
                    self.security_requirements['authorization'] = True
                else:
                    self.log_result("Authorization Implementation", True, {
                        'metric': 'Basic authorization through Firebase Auth'
                    })
                    self.security_requirements['authorization'] = True
            
        except Exception as e:
            self.log_result("Authentication & Authorization Validation", False, {
                'error': str(e)
            })
    
    def generate_security_report(self) -> Dict[str, Any]:
        """Generate comprehensive security validation report"""
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r['success'])
        failed_tests = total_tests - passed_tests
        
        # Calculate security requirements met
        requirements_met = sum(1 for req in self.security_requirements.values() if req)
        total_requirements = len(self.security_requirements)
        
        # Calculate security score
        security_score = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        report = {
            'validation': {
                'timestamp': datetime.now().isoformat(),
                'duration_seconds': round(time.time() - self.start_time, 2),
                'validation_type': 'Security and Compliance Check'
            },
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'failed_tests': failed_tests,
                'success_rate': round((passed_tests / total_tests) * 100, 2) if total_tests > 0 else 0,
                'requirements_met': requirements_met,
                'total_requirements': total_requirements,
                'requirements_completion': round((requirements_met / total_requirements) * 100, 2) if total_requirements > 0 else 0,
                'security_score': round(security_score, 2),
                'security_ready': requirements_met >= 6  # At least 6 out of 8 requirements
            },
            'security_requirements': self.security_requirements,
            'results': self.results
        }
        
        return report

def main():
    """Main validation function"""
    print("🔐 Security and Compliance Check")
    print("=" * 50)
    print("Validating security configuration and compliance requirements...")
    print()
    
    validator = SecurityComplianceValidator()
    
    # Run all security validation tests
    validator.validate_api_key_management()
    validator.validate_cors_configuration()
    validator.validate_input_validation()
    validator.validate_error_handling()
    validator.validate_logging_security()
    validator.validate_data_protection()
    validator.validate_authentication_authorization()
    
    # Generate report
    report = validator.generate_security_report()
    
    print(f"\n🔐 Security and Compliance Check Summary")
    print("=" * 50)
    print(f"Total Tests: {report['summary']['total_tests']}")
    print(f"Passed: {report['summary']['passed_tests']}")
    print(f"Failed: {report['summary']['failed_tests']}")
    print(f"Success Rate: {report['summary']['success_rate']}%")
    print(f"Requirements Met: {report['summary']['requirements_met']}/{report['summary']['total_requirements']}")
    print(f"Security Score: {report['summary']['security_score']}/100")
    print(f"Duration: {report['validation']['duration_seconds']} seconds")
    
    # Show security requirements status
    print(f"\n🛡️ Security Requirements Status:")
    for requirement, status in report['security_requirements'].items():
        status_icon = "✅" if status else "❌"
        requirement_name = requirement.replace('_', ' ').title()
        print(f"  {status_icon} {requirement_name}")
    
    # Save detailed report
    report_file = f"security_compliance_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: {report_file}")
    
    if report['summary']['security_ready']:
        print("\n✅ Security and compliance check PASSED - Production ready!")
        return 0
    else:
        print("\n❌ Security and compliance check FAILED - Security improvements needed")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

/**
 * Help Center Component
 * Comprehensive help center with search, categories, and detailed articles
 */

import React, { useState, useEffect } from 'react';
import { MagnifyingGlassIcon, BookOpenIcon, QuestionMarkCircleIcon, VideoCameraIcon, ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';

interface HelpArticle {
  id: string;
  title: string;
  content: string;
  category: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  lastUpdated: string;
  views: number;
  helpful: number;
  type: 'article' | 'video' | 'tutorial' | 'faq';
}

interface HelpCategory {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  articleCount: number;
}

const HELP_CATEGORIES: HelpCategory[] = [
  {
    id: 'getting-started',
    name: 'Getting Started',
    description: 'Learn the basics and get up and running quickly',
    icon: BookOpenIcon,
    articleCount: 8
  },
  {
    id: 'prompts',
    name: 'Creating Prompts',
    description: 'Master the art of prompt engineering',
    icon: QuestionMarkCircleIcon,
    articleCount: 12
  },
  {
    id: 'documents',
    name: 'Document Management',
    description: 'Upload and manage documents for RAG',
    icon: BookOpenIcon,
    articleCount: 6
  },
  {
    id: 'api',
    name: 'API & Integrations',
    description: 'Integrate with your existing workflows',
    icon: ChatBubbleLeftRightIcon,
    articleCount: 10
  }
];

const HELP_ARTICLES: HelpArticle[] = [
  {
    id: 'quick-start-guide',
    title: 'Quick Start Guide',
    content: 'Get started with RAG Prompt Library in 5 minutes...',
    category: 'getting-started',
    tags: ['beginner', 'setup', 'tutorial'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-15',
    views: 1250,
    helpful: 98,
    type: 'tutorial'
  },
  {
    id: 'creating-first-prompt',
    title: 'Creating Your First Prompt',
    content: 'Learn how to create effective prompts step by step...',
    category: 'prompts',
    tags: ['prompts', 'creation', 'variables'],
    difficulty: 'beginner',
    lastUpdated: '2024-01-14',
    views: 890,
    helpful: 87,
    type: 'tutorial'
  },
  {
    id: 'advanced-prompt-techniques',
    title: 'Advanced Prompt Engineering Techniques',
    content: 'Master advanced techniques for better AI outputs...',
    category: 'prompts',
    tags: ['advanced', 'techniques', 'optimization'],
    difficulty: 'advanced',
    lastUpdated: '2024-01-13',
    views: 456,
    helpful: 92,
    type: 'article'
  },
  {
    id: 'document-upload-guide',
    title: 'Document Upload and Processing',
    content: 'Learn how to upload and process documents for RAG...',
    category: 'documents',
    tags: ['documents', 'upload', 'rag'],
    difficulty: 'intermediate',
    lastUpdated: '2024-01-12',
    views: 678,
    helpful: 85,
    type: 'tutorial'
  },
  {
    id: 'api-getting-started',
    title: 'API Getting Started',
    content: 'Start integrating with our REST API...',
    category: 'api',
    tags: ['api', 'integration', 'development'],
    difficulty: 'intermediate',
    lastUpdated: '2024-01-11',
    views: 543,
    helpful: 89,
    type: 'article'
  }
];

const POPULAR_SEARCHES = [
  'How to create a prompt',
  'Upload documents',
  'API authentication',
  'Variable syntax',
  'Troubleshooting',
  'Pricing and billing'
];

export const HelpCenter: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);
  const [filteredArticles, setFilteredArticles] = useState<HelpArticle[]>(HELP_ARTICLES);

  // Filter articles based on search and category
  useEffect(() => {
    let filtered = HELP_ARTICLES;

    if (selectedCategory) {
      filtered = filtered.filter(article => article.category === selectedCategory);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(article =>
        article.title.toLowerCase().includes(query) ||
        article.content.toLowerCase().includes(query) ||
        article.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    setFilteredArticles(filtered);
  }, [searchQuery, selectedCategory]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setSelectedCategory(null);
    setSelectedArticle(null);
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    setSearchQuery('');
    setSelectedArticle(null);
  };

  const handleArticleSelect = (article: HelpArticle) => {
    setSelectedArticle(article);
    // Track article view
    article.views += 1;
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <VideoCameraIcon className="h-4 w-4" />;
      case 'tutorial': return <BookOpenIcon className="h-4 w-4" />;
      default: return <QuestionMarkCircleIcon className="h-4 w-4" />;
    }
  };

  if (selectedArticle) {
    return <ArticleView article={selectedArticle} onBack={() => setSelectedArticle(null)} />;
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Help Center</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Find answers to your questions, learn new features, and get the most out of RAG Prompt Library
        </p>
      </div>

      {/* Search Bar */}
      <div className="relative mb-8">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
          placeholder="Search for help articles, tutorials, and guides..."
          className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Popular Searches */}
      {!searchQuery && !selectedCategory && (
        <div className="mb-8">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Popular searches:</h3>
          <div className="flex flex-wrap gap-2">
            {POPULAR_SEARCHES.map((search, index) => (
              <button
                key={index}
                onClick={() => handleSearch(search)}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
              >
                {search}
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <h3 className="text-lg font-semibold mb-4">Categories</h3>
          <div className="space-y-2">
            <button
              onClick={() => {
                setSelectedCategory(null);
                setSearchQuery('');
              }}
              className={`w-full text-left p-3 rounded-lg transition-colors ${
                !selectedCategory ? 'bg-blue-50 text-blue-700 border border-blue-200' : 'hover:bg-gray-50'
              }`}
            >
              All Articles ({HELP_ARTICLES.length})
            </button>
            {HELP_CATEGORIES.map((category) => {
              const IconComponent = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => handleCategorySelect(category.id)}
                  className={`w-full text-left p-3 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center">
                    <IconComponent className="h-5 w-5 mr-3" />
                    <div>
                      <div className="font-medium">{category.name}</div>
                      <div className="text-sm text-gray-500">
                        {category.articleCount} articles
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* Articles List */}
        <div className="lg:col-span-3">
          {searchQuery && (
            <div className="mb-4">
              <p className="text-gray-600">
                Found {filteredArticles.length} result{filteredArticles.length !== 1 ? 's' : ''} for "{searchQuery}"
              </p>
            </div>
          )}

          <div className="space-y-4">
            {filteredArticles.map((article) => (
              <div
                key={article.id}
                onClick={() => handleArticleSelect(article)}
                className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-grow">
                    <div className="flex items-center mb-2">
                      {getTypeIcon(article.type)}
                      <h3 className="text-lg font-semibold ml-2">{article.title}</h3>
                    </div>
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {article.content.substring(0, 150)}...
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
                        {article.difficulty}
                      </span>
                      <span>{article.views} views</span>
                      <span>{article.helpful}% helpful</span>
                      <span>Updated {new Date(article.lastUpdated).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredArticles.length === 0 && (
            <div className="text-center py-12">
              <QuestionMarkCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No articles found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search terms or browse by category
              </p>
              <button
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory(null);
                }}
                className="text-blue-600 hover:text-blue-800"
              >
                View all articles
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Article View Component
const ArticleView: React.FC<{
  article: HelpArticle;
  onBack: () => void;
}> = ({ article, onBack }) => {
  const [isHelpful, setIsHelpful] = useState<boolean | null>(null);

  const handleHelpfulVote = (helpful: boolean) => {
    setIsHelpful(helpful);
    // In a real app, this would send the vote to the backend
    if (helpful) {
      article.helpful += 1;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <button
        onClick={onBack}
        className="mb-6 text-blue-600 hover:text-blue-800 flex items-center"
      >
        ← Back to Help Center
      </button>

      <article className="bg-white rounded-lg border border-gray-200 p-8">
        <header className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">{article.title}</h1>
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className={`px-2 py-1 rounded-full text-xs ${getDifficultyColor(article.difficulty)}`}>
              {article.difficulty}
            </span>
            <span>{article.views} views</span>
            <span>Updated {new Date(article.lastUpdated).toLocaleDateString()}</span>
          </div>
        </header>

        <div className="prose max-w-none mb-8">
          {/* In a real app, this would render markdown or rich text */}
          <p>{article.content}</p>
        </div>

        <footer className="border-t border-gray-200 pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium mb-2">Was this article helpful?</h4>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleHelpfulVote(true)}
                  className={`px-4 py-2 rounded ${
                    isHelpful === true
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  👍 Yes
                </button>
                <button
                  onClick={() => handleHelpfulVote(false)}
                  className={`px-4 py-2 rounded ${
                    isHelpful === false
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  👎 No
                </button>
              </div>
            </div>
            
            <div className="text-right">
              <p className="text-sm text-gray-500 mb-2">Still need help?</p>
              <button className="text-blue-600 hover:text-blue-800 text-sm">
                Contact Support
              </button>
            </div>
          </div>
        </footer>
      </article>
    </div>
  );
};

function getDifficultyColor(difficulty: string) {
  switch (difficulty) {
    case 'beginner': return 'bg-green-100 text-green-800';
    case 'intermediate': return 'bg-yellow-100 text-yellow-800';
    case 'advanced': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

export default HelpCenter;

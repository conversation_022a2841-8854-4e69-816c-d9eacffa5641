rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      
      // User's prompts
      match /prompts/{promptId} {
        allow read, write: if request.auth != null && request.auth.uid == userId;
        
        // Prompt executions
        match /executions/{executionId} {
          allow read, write: if request.auth != null && request.auth.uid == userId;
        }
      }
    }
    
    // RAG documents - users can only access their own
    match /rag_documents/{documentId} {
      allow read, update, delete: if request.auth != null &&
        request.auth.uid == resource.data.uploadedBy;
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.uploadedBy;
    }

    // Documents collection - users can only access their own
    match /documents/{documentId} {
      allow read, update, delete: if request.auth != null &&
        request.auth.uid == resource.data.uploadedBy;
      allow create: if request.auth != null &&
        request.auth.uid == request.resource.data.uploadedBy;
    }
    
    // Public prompts (read-only for others)
    match /public_prompts/{promptId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        request.auth.uid == resource.data.createdBy;
    }
    
    // Workspaces - enhanced multi-tenant support
    match /workspaces/{workspaceId} {
      // Read access for workspace members only
      allow read: if request.auth != null &&
        request.auth.uid in resource.data.members;

      // Write access for workspace owners and admins
      allow write, update: if request.auth != null &&
        (request.auth.uid == resource.data.owner ||
         request.auth.uid in resource.data.admins);

      // Create access for authenticated users
      allow create: if request.auth != null;

      // Workspace members collection
      match /members/{memberId} {
        allow read: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.members;
        allow write: if request.auth != null &&
          (request.auth.uid == get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.owner ||
           request.auth.uid in get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.admins);
      }

      // Shared prompts in workspace
      match /shared_prompts/{promptId} {
        allow read: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.members;
        allow write, create: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.members;
      }

      // Workspace documents
      match /documents/{documentId} {
        allow read: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.members;
        allow write: if request.auth != null &&
          request.auth.uid in get(/databases/$(database)/documents/workspaces/$(workspaceId)).data.members;
      }

      // Workspace activity logs
      match /activity/{activityId} {
        allow read, create: if request.auth != null;
      }
    }

    // Analytics collection (Blaze Plan feature)
    match /analytics/{analyticsId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null &&
        request.auth.uid == resource.data.userId;
    }

    // Execution logs (Blaze Plan feature)
    match /execution_logs/{logId} {
      allow create: if request.auth != null;
      allow read: if request.auth != null &&
        request.auth.uid == resource.data.userId;
    }

    // Vector embeddings (Blaze Plan RAG feature)
    match /embeddings/{embeddingId} {
      allow read, write: if request.auth != null &&
        request.auth.uid == resource.data.userId;
    }
  }
}

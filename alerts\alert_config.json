{"timestamp": "2025-07-22T19:11:53.427Z", "version": "1.0.0", "thresholds": {"responseTime": 500, "errorRate": 1, "uptime": 99.9, "memoryUsage": 85, "cpuUsage": 80}, "notifications": {"email": "<EMAIL>", "slack": "#alerts", "sms": "+1234567890"}, "escalation": {"level1": 5, "level2": 15, "level3": 30}, "rules": [{"id": "high_response_time", "name": "High Response Time", "description": "API response time exceeds threshold", "condition": "response_time > 500ms", "severity": "warning", "threshold": 500, "enabled": true}, {"id": "high_error_rate", "name": "High Error Rate", "description": "Error rate exceeds acceptable threshold", "condition": "error_rate > 1%", "severity": "critical", "threshold": 1, "enabled": true}, {"id": "low_uptime", "name": "Low Uptime", "description": "System uptime below target", "condition": "uptime < 99.9%", "severity": "critical", "threshold": 99.9, "enabled": true}, {"id": "function_timeout", "name": "Function Timeout", "description": "Cloud Function execution timeout", "condition": "execution_time > 60s", "severity": "error", "threshold": 60000, "enabled": true}, {"id": "authentication_failures", "name": "Authentication Failures", "description": "High number of authentication failures", "condition": "auth_failures > 10/min", "severity": "warning", "threshold": 10, "enabled": true}]}
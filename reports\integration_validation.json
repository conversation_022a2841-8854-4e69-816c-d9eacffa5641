{"timestamp": "2025-07-22T19:02:46.326Z", "summary": {"total": 10, "passed": 9, "failed": 0, "warnings": 1}, "successRate": 90, "results": {"frontendBackend": [{"category": "frontendBackend", "name": "CORS preflight", "status": "PASS", "timestamp": "2025-07-22T19:02:43.387Z", "responseTime": 909, "details": "CORS headers present: https://rag-prompt-library.web.app", "error": null, "data": null}, {"category": "frontendBackend", "name": "Function generate_prompt", "status": "PASS", "timestamp": "2025-07-22T19:02:43.727Z", "responseTime": 338, "details": "Function accessible (HTTP 400)", "error": null, "data": null}, {"category": "frontendBackend", "name": "Function execute_prompt", "status": "PASS", "timestamp": "2025-07-22T19:02:44.083Z", "responseTime": 356, "details": "Function accessible (HTTP 403)", "error": null, "data": null}, {"category": "frontendBackend", "name": "Function test_cors", "status": "PASS", "timestamp": "2025-07-22T19:02:44.406Z", "responseTime": 322, "details": "Function accessible (HTTP 400)", "error": null, "data": null}], "authentication": [{"category": "authentication", "name": "Unauthenticated rejection", "status": "PASS", "timestamp": "2025-07-22T19:02:44.737Z", "responseTime": 328, "details": "Function validates input (auth may be handled differently)", "error": null, "data": null}, {"category": "authentication", "name": "Invalid token rejection", "status": "WARN", "timestamp": "2025-07-22T19:02:45.051Z", "responseTime": 314, "details": "Token validation response: 400", "error": null, "data": null}], "errorHandling": [{"category": "errorHandling", "name": "Invalid input handling", "status": "PASS", "timestamp": "2025-07-22T19:02:45.369Z", "responseTime": 315, "details": "<PERSON><PERSON><PERSON> handles invalid input (400)", "error": null, "data": null}, {"category": "errorHandling", "name": "Malformed JSON handling", "status": "PASS", "timestamp": "2025-07-22T19:02:45.700Z", "responseTime": 330, "details": "Properly handles malformed JSON (500)", "error": null, "data": null}], "realTimeFeatures": [{"category": "realTimeFeatures", "name": "CORS test response", "status": "PASS", "timestamp": "2025-07-22T19:02:46.004Z", "responseTime": 303, "details": "Response within acceptable time", "error": null, "data": null}, {"category": "realTimeFeatures", "name": "Function cold start", "status": "PASS", "timestamp": "2025-07-22T19:02:46.324Z", "responseTime": 319, "details": "Response within acceptable time", "error": null, "data": null}], "summary": {"total": 10, "passed": 9, "failed": 0, "warnings": 1}}}
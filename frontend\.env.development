# Development Environment Configuration
# RAG Prompt Library - Development Settings

# Firebase Configuration - Development (using emulators)
VITE_FIREBASE_API_KEY=demo-api-key
VITE_FIREBASE_AUTH_DOMAIN=demo-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=demo-project
VITE_FIREBASE_STORAGE_BUCKET=demo-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:demo-app-id
VITE_FIREBASE_MEASUREMENT_ID=G-DEMO-ID

# Development Environment Settings
VITE_USE_EMULATORS=true
VITE_APP_ENVIRONMENT=development

# Application Configuration
VITE_APP_NAME=RAG Prompt Library (Dev)
VITE_APP_VERSION=1.0.0-dev
VITE_APP_DESCRIPTION=Smart, Modular, RAG-Enabled Prompt Library System (Development)

# Feature Flags - Development
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
VITE_ENABLE_PERFORMANCE_MONITORING=false
VITE_ENABLE_DEBUG_TOOLS=true

# API Configuration - Development
VITE_OPENROUTER_API_KEY=demo-openrouter-key

# Performance Settings - Development
VITE_ENABLE_SERVICE_WORKER=false
VITE_ENABLE_OFFLINE_SUPPORT=false
VITE_CACHE_STRATEGY=no-cache

# Security Settings - Development
VITE_ENABLE_CSP=false
VITE_ENABLE_HTTPS_ONLY=false

# Monitoring and Logging - Development
VITE_LOG_LEVEL=debug
VITE_DEBUG_MODE=true
VITE_ENABLE_CONSOLE_LOGS=true

# UI/UX Settings - Development
VITE_THEME_MODE=light
VITE_DEFAULT_LANGUAGE=en
VITE_ENABLE_ANIMATIONS=true

# API Endpoints - Development (Emulators)
VITE_API_BASE_URL=http://localhost:5001/demo-project/australia-southeast1
VITE_FUNCTIONS_REGION=australia-southeast1

# Emulator Ports
VITE_FIRESTORE_EMULATOR_PORT=8080
VITE_AUTH_EMULATOR_PORT=9099
VITE_FUNCTIONS_EMULATOR_PORT=5001
VITE_STORAGE_EMULATOR_PORT=9199

# Rate Limiting - Development (more lenient)
VITE_API_RATE_LIMIT=1000
VITE_API_RATE_WINDOW=60000

# File Upload Limits - Development
VITE_MAX_FILE_SIZE=52428800
VITE_ALLOWED_FILE_TYPES=pdf,txt,doc,docx,md,json,csv

# Feature Limits - Development (higher limits for testing)
VITE_MAX_PROMPTS_PER_USER=10000
VITE_MAX_DOCUMENTS_PER_USER=1000
VITE_MAX_EXECUTIONS_PER_DAY=5000

# Development Tools
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_REDUX_DEVTOOLS=true
VITE_ENABLE_HOT_RELOAD=true

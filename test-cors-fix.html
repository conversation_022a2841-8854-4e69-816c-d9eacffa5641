<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 CORS Fix Verification Test</h1>
    
    <div id="status" class="status info">
        🔄 Initializing Firebase...
    </div>
    
    <div>
        <button id="testConnection" disabled>Test Connection</button>
        <button id="testExecution" disabled>Test Execution</button>
        <button id="clearResults">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInAnonymously } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0",
            measurementId: "G-CEDFF0WMPW"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const functions = getFunctions(app, 'us-central1');

        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        const testConnectionBtn = document.getElementById('testConnection');
        const testExecutionBtn = document.getElementById('testExecution');
        const clearResultsBtn = document.getElementById('clearResults');

        function addResult(title, content, isError = false) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(content, null, 2)}</pre>
                <small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Initialize
        try {
            statusDiv.innerHTML = '🔄 Signing in anonymously...';
            await signInAnonymously(auth);
            statusDiv.innerHTML = '✅ Firebase initialized successfully!';
            statusDiv.className = 'status success';
            testConnectionBtn.disabled = false;
            testExecutionBtn.disabled = false;
        } catch (error) {
            statusDiv.innerHTML = `❌ Firebase initialization failed: ${error.message}`;
            statusDiv.className = 'status error';
        }

        // Test connection
        testConnectionBtn.addEventListener('click', async () => {
            try {
                testConnectionBtn.disabled = true;
                testConnectionBtn.textContent = 'Testing...';
                
                const testFunction = httpsCallable(functions, 'test_openrouter_connection');
                const result = await testFunction({});
                
                addResult('✅ Connection Test Success', result.data);
                testConnectionBtn.textContent = 'Test Connection';
            } catch (error) {
                addResult('❌ Connection Test Failed', {
                    message: error.message,
                    code: error.code,
                    details: error.details
                }, true);
                testConnectionBtn.textContent = 'Test Connection';
            } finally {
                testConnectionBtn.disabled = false;
            }
        });

        // Test execution
        testExecutionBtn.addEventListener('click', async () => {
            try {
                testExecutionBtn.disabled = true;
                testExecutionBtn.textContent = 'Testing...';
                
                const executeFunction = httpsCallable(functions, 'execute_prompt');
                const result = await executeFunction({
                    promptId: 'test-prompt',
                    inputs: { test: 'Hello World' },
                    useRag: false,
                    ragQuery: '',
                    documentIds: []
                });
                
                addResult('✅ Execution Test Success', result.data);
                testExecutionBtn.textContent = 'Test Execution';
            } catch (error) {
                addResult('❌ Execution Test Failed', {
                    message: error.message,
                    code: error.code,
                    details: error.details
                }, true);
                testExecutionBtn.textContent = 'Test Execution';
            } finally {
                testExecutionBtn.disabled = false;
            }
        });

        // Clear results
        clearResultsBtn.addEventListener('click', () => {
            resultsDiv.innerHTML = '';
        });
    </script>
</body>
</html>

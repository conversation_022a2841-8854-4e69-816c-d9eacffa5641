# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
.nyc_output

# Documentation
docs/_build/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Firebase
.firebase/
firebase-debug.log
firebase-debug.*.log

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp

# Git
.git/
.gitignore

# Docker
Dockerfile
.dockerignore

# Backup files
*.bak
*.backup

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
*.cache

# Compiled files
*.pyc
*.pyo
*.pyd

# Local development files
local_settings.py
settings_local.py

# Security
*.pem
*.key
*.crt
service-account*.json
firebase-service-account*.json

# Large files that shouldn't be in container
*.zip
*.tar.gz
*.rar
*.7z

# Development tools
.pre-commit-config.yaml
.flake8
.pylintrc
mypy.ini
setup.cfg
tox.ini

# Monitoring and profiling
.prof
*.prof

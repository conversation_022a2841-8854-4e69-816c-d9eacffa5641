#!/usr/bin/env python3
"""
Infrastructure Components Validation Script
Tests all external services and infrastructure components
"""
import requests
import os
import time
from typing import Dict, List, Tuple

def test_firebase_services() -> Dict[str, bool]:
    """Test Firebase emulator services"""
    results = {}
    
    firebase_services = {
        "Firebase Hub": "http://127.0.0.1:4400",
        "Firestore": "http://127.0.0.1:8081", 
        "Auth": "http://127.0.0.1:9100",
        "Storage": "http://127.0.0.1:9200",
        "Functions UI": "http://127.0.0.1:4001/functions"
    }
    
    for service, url in firebase_services.items():
        try:
            response = requests.get(url, timeout=5)
            results[service] = response.status_code in [200, 404]  # 404 is OK for some endpoints
            print(f"✅ {service}: Connected (Status: {response.status_code})")
        except Exception as e:
            results[service] = False
            print(f"❌ {service}: Failed - {e}")
    
    return results

def test_external_apis() -> Dict[str, bool]:
    """Test external API connectivity"""
    results = {}
    
    # Test OpenRouter API (if API key is available)
    openrouter_key = os.getenv('OPENROUTER_API_KEY')
    if openrouter_key:
        try:
            headers = {
                'Authorization': f'Bearer {openrouter_key}',
                'Content-Type': 'application/json'
            }
            response = requests.get('https://openrouter.ai/api/v1/models', headers=headers, timeout=10)
            results["OpenRouter API"] = response.status_code == 200
            print(f"✅ OpenRouter API: Connected (Status: {response.status_code})")
        except Exception as e:
            results["OpenRouter API"] = False
            print(f"❌ OpenRouter API: Failed - {e}")
    else:
        results["OpenRouter API"] = False
        print("⚠️ OpenRouter API: No API key found")
    
    # Test OpenAI API (if API key is available)
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        try:
            headers = {
                'Authorization': f'Bearer {openai_key}',
                'Content-Type': 'application/json'
            }
            response = requests.get('https://api.openai.com/v1/models', headers=headers, timeout=10)
            results["OpenAI API"] = response.status_code == 200
            print(f"✅ OpenAI API: Connected (Status: {response.status_code})")
        except Exception as e:
            results["OpenAI API"] = False
            print(f"❌ OpenAI API: Failed - {e}")
    else:
        results["OpenAI API"] = False
        print("⚠️ OpenAI API: No API key found")
    
    # Test Anthropic API (if API key is available)
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    if anthropic_key:
        try:
            headers = {
                'x-api-key': anthropic_key,
                'Content-Type': 'application/json'
            }
            # Test with a simple request to check connectivity
            response = requests.get('https://api.anthropic.com/v1/messages', headers=headers, timeout=10)
            # Anthropic might return 400 for GET request, but that means it's accessible
            results["Anthropic API"] = response.status_code in [200, 400, 405]
            print(f"✅ Anthropic API: Connected (Status: {response.status_code})")
        except Exception as e:
            results["Anthropic API"] = False
            print(f"❌ Anthropic API: Failed - {e}")
    else:
        results["Anthropic API"] = False
        print("⚠️ Anthropic API: No API key found")
    
    return results

def test_database_connectivity() -> Dict[str, bool]:
    """Test database connectivity"""
    results = {}
    
    # Test Redis (if available)
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, decode_responses=True)
        redis_client.ping()
        results["Redis"] = True
        print("✅ Redis: Connected")
    except ImportError:
        results["Redis"] = False
        print("⚠️ Redis: Python client not installed")
    except Exception as e:
        results["Redis"] = False
        print(f"❌ Redis: Failed - {e}")
    
    # Test Pinecone (if API key is available)
    pinecone_key = os.getenv('PINECONE_API_KEY')
    if pinecone_key:
        try:
            headers = {
                'Api-Key': pinecone_key,
                'Content-Type': 'application/json'
            }
            response = requests.get('https://api.pinecone.io/indexes', headers=headers, timeout=10)
            results["Pinecone"] = response.status_code == 200
            print(f"✅ Pinecone: Connected (Status: {response.status_code})")
        except Exception as e:
            results["Pinecone"] = False
            print(f"❌ Pinecone: Failed - {e}")
    else:
        results["Pinecone"] = False
        print("⚠️ Pinecone: No API key found")
    
    return results

def test_application_endpoints() -> Dict[str, bool]:
    """Test application-specific endpoints"""
    results = {}
    
    # Test frontend application
    try:
        response = requests.get('http://localhost:3000', timeout=10)
        results["Frontend App"] = response.status_code == 200
        print(f"✅ Frontend App: Running (Status: {response.status_code})")
    except Exception as e:
        results["Frontend App"] = False
        print(f"❌ Frontend App: Failed - {e}")
    
    return results

def print_infrastructure_report(all_results: Dict[str, Dict[str, bool]]):
    """Print comprehensive infrastructure report"""
    print("\n" + "=" * 70)
    print("INFRASTRUCTURE COMPONENTS VALIDATION REPORT")
    print("=" * 70)
    
    total_services = 0
    passing_services = 0
    
    for category, results in all_results.items():
        print(f"\n📋 {category.upper()}:")
        print("-" * 40)
        
        for service, status in results.items():
            status_icon = "✅ PASS" if status else "❌ FAIL"
            print(f"  {service}: {status_icon}")
            total_services += 1
            if status:
                passing_services += 1
    
    print("\n" + "=" * 70)
    success_rate = (passing_services / total_services * 100) if total_services > 0 else 0
    print(f"OVERALL INFRASTRUCTURE HEALTH: {passing_services}/{total_services} services ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 INFRASTRUCTURE STATUS: HEALTHY")
    elif success_rate >= 60:
        print("⚠️ INFRASTRUCTURE STATUS: DEGRADED")
    else:
        print("🚨 INFRASTRUCTURE STATUS: CRITICAL")
    
    print("=" * 70)

if __name__ == "__main__":
    print("Starting Infrastructure Components Validation...")
    print("Testing connectivity to all external services and infrastructure components...")
    print("\n" + "-" * 50)
    
    # Run all validation tests
    all_results = {
        "Firebase Services": test_firebase_services(),
        "External APIs": test_external_apis(), 
        "Database Services": test_database_connectivity(),
        "Application Endpoints": test_application_endpoints()
    }
    
    # Print comprehensive report
    print_infrastructure_report(all_results)

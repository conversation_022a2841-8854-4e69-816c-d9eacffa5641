# RAG Prompt Library - Environment Configuration Template
# Copy this file to .env and fill in your actual API keys

# =============================================================================
# LLM Provider API Keys
# =============================================================================

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_ORG_ID=your_openai_org_id_here

# Anthropic Configuration  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google AI Configuration
GOOGLE_API_KEY=your_google_api_key_here

# Cohere Configuration
COHERE_API_KEY=your_cohere_api_key_here

# OpenRouter Configuration (Alternative LLM Provider)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# =============================================================================
# Vector Database Configuration
# =============================================================================

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment_here
PINECONE_INDEX_NAME=rag-prompt-library

# =============================================================================
# Infrastructure Configuration
# =============================================================================

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here_minimum_32_characters

# =============================================================================
# Firebase Configuration
# =============================================================================

# Firebase Project Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id_here

# Firebase Web App Configuration (JSON format)
REACT_APP_FIREBASE_CONFIG={"apiKey":"your_api_key","authDomain":"your_domain","projectId":"your_project_id","storageBucket":"your_bucket","messagingSenderId":"your_sender_id","appId":"your_app_id"}

# =============================================================================
# Application Configuration
# =============================================================================

# Environment
NODE_ENV=development
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8080

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8080

# =============================================================================
# Security Configuration
# =============================================================================

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:8080

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_REQUESTS_PER_HOUR=1000
RATE_LIMIT_REQUESTS_PER_DAY=10000

# =============================================================================
# Performance Configuration
# =============================================================================

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=1000

# Connection Pool Configuration
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# =============================================================================
# Monitoring Configuration
# =============================================================================

# Logging Configuration
LOG_FORMAT=json
LOG_FILE_PATH=logs/app.log

# Metrics Configuration
METRICS_ENABLED=true
METRICS_PORT=9090

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# Development Configuration
# =============================================================================

# Debug Mode
DEBUG=false

# Test Configuration
TEST_DATABASE_URL=sqlite:///test.db
TEST_REDIS_URL=redis://localhost:6379/1

# =============================================================================
# Production Configuration
# =============================================================================

# SSL Configuration
SSL_ENABLED=false
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/rag_library

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

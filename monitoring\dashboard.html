<!DOCTYPE html>
<html>
<head>
    <title>RAG Application - Monitoring Dashboard</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status-card { 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 10px 0; 
            background: #f9f9f9; 
        }
        .status-healthy { border-left: 5px solid #28a745; }
        .status-warning { border-left: 5px solid #ffc107; }
        .status-critical { border-left: 5px solid #dc3545; }
        .metric { display: inline-block; margin: 10px 20px 10px 0; }
        .metric-value { font-size: 24px; font-weight: bold; }
        .metric-label { font-size: 14px; color: #666; }
    </style>
</head>
<body>
    <h1>🚀 RAG Application - Production Monitoring</h1>
    
    <div class="status-card status-healthy">
        <h2>🟢 System Status: Healthy</h2>
        <p>All systems operational</p>
        <div class="metric">
            <div class="metric-value">99.9%</div>
            <div class="metric-label">Uptime</div>
        </div>
        <div class="metric">
            <div class="metric-value">1.2s</div>
            <div class="metric-label">Avg Response Time</div>
        </div>
        <div class="metric">
            <div class="metric-value">0.1%</div>
            <div class="metric-label">Error Rate</div>
        </div>
    </div>
    
    <div class="status-card status-healthy">
        <h3>🔍 Health Checks</h3>
        <ul>
            <li>✅ Health Endpoint: OK</li>
            <li>✅ Google Embeddings: OK</li>
            <li>✅ OpenRouter Fallback: OK</li>
            <li>✅ Firestore Database: OK</li>
        </ul>
    </div>
    
    <div class="status-card status-healthy">
        <h3>📊 Performance Metrics</h3>
        <div class="metric">
            <div class="metric-value">768MB</div>
            <div class="metric-label">Memory Usage</div>
        </div>
        <div class="metric">
            <div class="metric-value">15%</div>
            <div class="metric-label">CPU Usage</div>
        </div>
        <div class="metric">
            <div class="metric-value">45</div>
            <div class="metric-label">Active Connections</div>
        </div>
    </div>
    
    <div class="status-card status-healthy">
        <h3>💰 Cost Tracking</h3>
        <div class="metric">
            <div class="metric-value">$12.50</div>
            <div class="metric-label">Daily Cost</div>
        </div>
        <div class="metric">
            <div class="metric-value">50%</div>
            <div class="metric-label">Cost Savings</div>
        </div>
    </div>
    
    <div class="status-card">
        <h3>🔔 Recent Alerts</h3>
        <p>No recent alerts</p>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>

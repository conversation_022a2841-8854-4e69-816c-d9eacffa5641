import React from 'react';
import { screen, fireEvent, waitFor, act, render, cleanup } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

// Mock the AuthContext module
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    currentUser: {
      uid: 'test-user-id',
      email: '<EMAIL>',
      displayName: 'Test User'
    },
    userProfile: null,
    loading: false,
    signup: vi.fn(() => Promise.resolve()),
    login: vi.fn(() => Promise.resolve()),
    loginWithGoogle: vi.fn(() => Promise.resolve()),
    logout: vi.fn(() => Promise.resolve()),
    updateUserProfile: vi.fn(() => Promise.resolve())
  })),
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

import { DocumentUpload } from '../DocumentUpload';
import { render } from '@testing-library/react';

// Mock storage object
const mockStorage = {
  ref: vi.fn(() => ({
    getDownloadURL: vi.fn(() => Promise.resolve('https://example.com/file.pdf'))
  })),
  uploadBytesResumable: vi.fn(() => ({
    on: vi.fn((event, progress, error, complete) => {
      // Simulate upload progress
      setTimeout(() => progress({ bytesTransferred: 50, totalBytes: 100 }), 10);
      setTimeout(() => complete(), 20);
    }),
    cancel: vi.fn()
  }))
};

// Mock Firebase modules
vi.mock('firebase/storage', () => ({
  ref: vi.fn(() => mockStorage.ref()),
  uploadBytesResumable: vi.fn(() => mockStorage.uploadBytesResumable()),
  getDownloadURL: vi.fn(() => mockStorage.ref().getDownloadURL())
}));

vi.mock('firebase/firestore', () => ({
  collection: vi.fn(() => mockFirestore.collection()),
  addDoc: vi.fn(() => mockFirestore.addDoc()),
  serverTimestamp: vi.fn(() => mockFirestore.serverTimestamp())
}));

vi.mock('../../../config/firebase', () => ({
  storage: {},
  db: {},
  auth: {}
}));

// Mock useAuth hook
const mockUseAuth = vi.fn();
vi.mock('../../contexts/AuthContext', () => ({
  useAuth: mockUseAuth
}));

// Mock Toast hooks
vi.mock('../../common/Toast', () => ({
  useSuccessToast: () => vi.fn(),
  useErrorToast: () => vi.fn(),
  useWarningToast: () => vi.fn(),
  useInfoToast: () => vi.fn()
}));

describe('DocumentUpload', () => {
  const mockOnUploadComplete = vi.fn();
  const mockUser = {
    uid: 'test-user-id',
    email: '<EMAIL>'
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock for useAuth
    mockUseAuth.mockReturnValue({
      currentUser: mockUser,
      loading: false,
      login: vi.fn(),
      signup: vi.fn(),
      logout: vi.fn(),
      loginWithGoogle: vi.fn()
    });
  });

  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });

  it('renders upload area correctly', () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    expect(screen.getByText('Upload Documents')).toBeDefined();
    expect(screen.getByText(/drag and drop files here/i)).toBeDefined();
    expect(screen.getByText(/supported formats/i)).toBeDefined();
    expect(screen.getByRole('button', { name: /select files/i })).toBeDefined();
  });

  it('shows file input when click to upload', () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    const selectButton = screen.getByRole('button', { name: /select files/i });
    expect(selectButton).toBeDefined();

    // Click should trigger file input
    act(() => { fireEvent.click(selectButton); });

    // File input should exist (even if hidden)
    const fileInput = screen.getByLabelText(/file input for document upload/i);
    expect(fileInput).toBeDefined();
  });

  it('validates file types correctly', async () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    // Create a mock file with unsupported type
    const invalidFile = new File(['content'], 'test.jpg', { type: 'image/jpeg' });

    const fileInput = screen.getByLabelText(/file input for document upload/i);

    act(() => {
      fireEvent.change(fileInput, {
        target: { files: [invalidFile] }
      });
    });

    // Should show error for unsupported file type
    await waitFor(() => {
      expect(screen.getByText(/unsupported file type/i)).toBeDefined();
    }, { timeout: 1000 });
  });

  it('validates file size correctly', async () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    // Create a mock file that's too large (>10MB)
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', { type: 'application/pdf' });

    const fileInput = screen.getByLabelText(/file input for document upload/i);

    act(() => {
      fireEvent.change(fileInput, {
        target: { files: [largeFile] }
      });
    });

    // Should show error for file too large
    await waitFor(() => {
      expect(screen.getByText(/file too large/i)).toBeDefined();
    }, { timeout: 1000 });
  });

  it('accepts valid files', async () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    // Create a valid PDF file
    const validFile = new File(['pdf content'], 'test.pdf', { type: 'application/pdf' });

    const fileInput = screen.getByLabelText(/file input for document upload/i);

    act(() => {
      fireEvent.change(fileInput, {
        target: { files: [validFile] }
      });
    });

    // Should show the file in the upload list
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeDefined();
    }, { timeout: 1000 });
  });

  it('allows removing files from upload queue', async () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    // Add a valid file
    const validFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = screen.getByLabelText(/file input for document upload/i);

    act(() => {
      fireEvent.change(fileInput, {
        target: { files: [validFile] }
      });
    });

    // Wait for file to appear
    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeDefined();
    }, { timeout: 1000 });

    // Click remove button
    const removeButton = screen.getByRole('button', { name: /remove/i });
    act(() => { fireEvent.click(removeButton); });

    // File should be removed
    await waitFor(() => {
      expect(screen.queryByText('test.pdf')).toBeNull();
    }, { timeout: 1000 });
  });

  it('handles drag and drop events', () => {
    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    const uploadArea = screen.getByText('Drag and drop files here, or click to select files').closest('div');

    // Test drag enter
    act(() => {
      fireEvent.dragEnter(uploadArea!);
      fireEvent.dragOver(uploadArea!);
    });
    expect(uploadArea?.className).toContain('border-blue-400'); // Should highlight on drag

    // Test drag leave
    act(() => { fireEvent.dragLeave(uploadArea!); });
    expect(uploadArea?.className).not.toContain('border-blue-400');

    // Test drop
    const validFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    const dropEvent = new Event('drop', { bubbles: true });
    Object.defineProperty(dropEvent, 'dataTransfer', {
      value: {
        files: [validFile]
      }
    });

    fireEvent(uploadArea!, dropEvent);
    
    // Should process the dropped file
    expect(screen.getByText('test.pdf')).toBeDefined();
  });

  it.skip('requires authentication', () => {
    mockUseAuth.mockReturnValue({
      currentUser: null,
      loading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
      signInWithGoogle: vi.fn()
    });

    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    // Try to add a file when not authenticated
    const validFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = screen.getByLabelText(/file input for document upload/i);

    act(() => {
      fireEvent.change(fileInput, {
        target: { files: [validFile] }
      });
    });

    // Should not show any files or upload buttons when not authenticated
    expect(screen.queryByText('test.pdf')).toBeNull();
    expect(screen.queryByRole('button', { name: /upload all/i })).toBeNull();
  });

  it('shows upload progress', async () => {
    const { uploadBytesResumable } = await import('firebase/storage');
    const mockUploadTask = {
      on: vi.fn((event, progressCallback, errorCallback, completeCallback) => {
        // Simulate progress
        setTimeout(() => progressCallback({ bytesTransferred: 50, totalBytes: 100 }), 100);
        setTimeout(() => completeCallback(), 200);
      }),
      snapshot: {
        ref: {}
      }
    };

    (uploadBytesResumable as vi.Mock).mockReturnValue(mockUploadTask);

    render(<DocumentUpload onUploadComplete={mockOnUploadComplete} />);

    // Add a file and start upload
    const validFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = screen.getByLabelText(/file input for document upload/i);

    act(() => {
      fireEvent.change(fileInput, {
        target: { files: [validFile] }
      });
    });

    await waitFor(() => {
      expect(screen.getByText('test.pdf')).toBeDefined();
    }, { timeout: 1000 });

    // Start upload
    const uploadButton = screen.getByRole('button', { name: /upload all/i });
    act(() => { fireEvent.click(uploadButton); });

    // Should show progress
    await waitFor(() => {
      expect(screen.getByText(/uploading/i)).toBeDefined();
    }, { timeout: 1000 });
  });
});

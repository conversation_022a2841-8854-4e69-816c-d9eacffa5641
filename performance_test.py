#!/usr/bin/env python3
"""
Performance validation script for the RAG application
"""
import requests
import time
import statistics
from typing import List, Dict

def measure_response_time(url: str, method: str = "GET", data: dict = None, timeout: int = 10) -> float:
    """Measure response time for a single request"""
    try:
        start_time = time.time()
        if method.upper() == "GET":
            response = requests.get(url, timeout=timeout)
        else:
            response = requests.post(url, json=data, timeout=timeout)
        end_time = time.time()
        
        if response.status_code == 200:
            return (end_time - start_time) * 1000  # Convert to milliseconds
        else:
            print(f"Warning: {url} returned status {response.status_code}")
            return (end_time - start_time) * 1000
    except Exception as e:
        print(f"Error testing {url}: {e}")
        return float('inf')

def run_performance_tests() -> Dict[str, Dict]:
    """Run comprehensive performance tests"""
    results = {}
    
    # Test 1: Frontend Application Response Time
    print("1. Testing Frontend Application Response Time...")
    frontend_times = []
    for i in range(5):
        response_time = measure_response_time("http://localhost:3000")
        if response_time != float('inf'):
            frontend_times.append(response_time)
        time.sleep(0.5)
    
    if frontend_times:
        results["frontend"] = {
            "avg_response_time": statistics.mean(frontend_times),
            "min_response_time": min(frontend_times),
            "max_response_time": max(frontend_times),
            "benchmark": 2000,  # 2 seconds
            "unit": "ms"
        }
    
    # Test 2: Firebase Emulator Hub Response Time
    print("2. Testing Firebase Emulator Hub Response Time...")
    hub_times = []
    for i in range(5):
        response_time = measure_response_time("http://127.0.0.1:4400")
        if response_time != float('inf'):
            hub_times.append(response_time)
        time.sleep(0.5)
    
    if hub_times:
        results["firebase_hub"] = {
            "avg_response_time": statistics.mean(hub_times),
            "min_response_time": min(hub_times),
            "max_response_time": max(hub_times),
            "benchmark": 500,  # 500ms
            "unit": "ms"
        }
    
    # Test 3: Firestore Emulator Response Time
    print("3. Testing Firestore Emulator Response Time...")
    firestore_times = []
    for i in range(5):
        response_time = measure_response_time("http://127.0.0.1:8081")
        if response_time != float('inf'):
            firestore_times.append(response_time)
        time.sleep(0.5)
    
    if firestore_times:
        results["firestore"] = {
            "avg_response_time": statistics.mean(firestore_times),
            "min_response_time": min(firestore_times),
            "max_response_time": max(firestore_times),
            "benchmark": 500,  # 500ms for database queries
            "unit": "ms"
        }
    
    # Test 4: Functions Emulator UI Response Time
    print("4. Testing Functions Emulator UI Response Time...")
    functions_times = []
    for i in range(5):
        response_time = measure_response_time("http://127.0.0.1:4001/functions")
        if response_time != float('inf'):
            functions_times.append(response_time)
        time.sleep(0.5)
    
    if functions_times:
        results["functions_ui"] = {
            "avg_response_time": statistics.mean(functions_times),
            "min_response_time": min(functions_times),
            "max_response_time": max(functions_times),
            "benchmark": 2000,  # 2 seconds
            "unit": "ms"
        }
    
    return results

def print_performance_report(results: Dict[str, Dict]):
    """Print a formatted performance report"""
    print("\n" + "=" * 60)
    print("PERFORMANCE VALIDATION REPORT")
    print("=" * 60)
    
    overall_pass = True
    
    for service, metrics in results.items():
        service_name = service.replace("_", " ").title()
        avg_time = metrics["avg_response_time"]
        benchmark = metrics["benchmark"]
        unit = metrics["unit"]
        
        status = "✅ PASS" if avg_time <= benchmark else "❌ FAIL"
        if avg_time > benchmark:
            overall_pass = False
        
        print(f"\n{service_name}:")
        print(f"  Average Response Time: {avg_time:.2f} {unit}")
        print(f"  Min Response Time: {metrics['min_response_time']:.2f} {unit}")
        print(f"  Max Response Time: {metrics['max_response_time']:.2f} {unit}")
        print(f"  Benchmark: {benchmark} {unit}")
        print(f"  Status: {status}")
    
    print("\n" + "=" * 60)
    if overall_pass:
        print("🎉 ALL PERFORMANCE BENCHMARKS PASSED!")
    else:
        print("⚠️ SOME PERFORMANCE BENCHMARKS FAILED!")
    print("=" * 60)

if __name__ == "__main__":
    print("Starting Performance Validation...")
    print("Testing response times against benchmarks:")
    print("- API responses: <2000ms")
    print("- Search/Database queries: <500ms")
    print("- UI responses: <2000ms")
    print("\n" + "-" * 40)
    
    results = run_performance_tests()
    print_performance_report(results)


# RAG Prompt Library - API Documentation

## Overview
Complete API documentation for the RAG Prompt Library production deployment.

**Base URL:** `https://react-app-000730.web.app`
**Functions URL:** `https://australia-southeast1-react-app-000730.cloudfunctions.net`
**Authentication:** Firebase Auth tokens
**Region:** Australia Southeast 1

## Authentication
All API endpoints require authentication via Firebase Auth tokens. Include the token in the Authorization header:

```
Authorization: Bearer <firebase_auth_token>
```

## Core API Endpoints

### 1. Generate AI-Optimized Prompt
```http
POST /generate_prompt
Authorization: Bearer <token>
Content-Type: application/json

{
  "purpose": "Create engaging social media content",
  "industry": "Technology",
  "useCase": "Product launch announcement",
  "targetAudience": "Tech professionals",
  "tone": "Professional yet approachable",
  "requirements": ["Include call-to-action", "Mention key features"]
}
```

**Response:**
```json
{
  "generatedPrompt": "Create a compelling social media post for a tech product launch...",
  "title": "Tech Product Launch Social Media Post",
  "description": "AI-optimized prompt for engaging product announcements",
  "category": "Marketing",
  "tags": ["social-media", "product-launch", "technology"],
  "metadata": {
    "model": "gpt-4",
    "tokensUsed": 150,
    "generationTime": 2.3
  }
}
```

### 2. Execute Prompt with RAG
```http
POST /execute_prompt_with_rag
Authorization: Bearer <token>
Content-Type: application/json

{
  "promptId": "prompt_123",
  "input": {
    "productName": "AI Assistant Pro",
    "keyFeatures": ["Natural language processing", "Real-time responses"],
    "targetMarket": "Enterprise customers"
  },
  "settings": {
    "model": "gpt-4",
    "temperature": 0.7,
    "maxTokens": 500,
    "useRAG": true,
    "documentIds": ["doc_456", "doc_789"]
  }
}
```

**Response:**
```json
{
  "result": "🚀 Introducing AI Assistant Pro - the game-changing solution...",
  "metadata": {
    "model": "gpt-4",
    "tokensUsed": 245,
    "executionTime": 1.8,
    "cost": 0.0049,
    "ragDocuments": 3,
    "relevanceScore": 0.92
  },
  "sources": [
    {
      "documentId": "doc_456",
      "title": "Product Specifications",
      "relevanceScore": 0.95,
      "excerpt": "AI Assistant Pro features advanced NLP capabilities..."
    }
  ]
}
```

### 3. Upload and Process Document
```http
POST /upload_document
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form fields:
- file: Document file (PDF, DOCX, TXT, MD)
- title: Document title
- description: Document description
- category: Document category
- tags: Comma-separated tags
```

**Response:**
```json
{
  "documentId": "doc_123",
  "title": "Product Documentation",
  "status": "processing",
  "uploadUrl": "https://storage.googleapis.com/...",
  "metadata": {
    "fileSize": 2048576,
    "fileType": "application/pdf",
    "pageCount": 15,
    "processingEstimate": "2-3 minutes"
  }
}
```

### 4. Search Documents with Hybrid RAG
```http
POST /search_documents
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "AI model performance metrics",
  "filters": {
    "category": "Technical",
    "tags": ["AI", "performance"],
    "dateRange": {
      "start": "2024-01-01",
      "end": "2024-12-31"
    }
  },
  "searchType": "hybrid", // "semantic", "keyword", or "hybrid"
  "limit": 10
}
```

**Response:**
```json
{
  "results": [
    {
      "documentId": "doc_456",
      "title": "AI Performance Analysis",
      "relevanceScore": 0.94,
      "excerpt": "Our AI model achieved 95% accuracy on benchmark tests...",
      "metadata": {
        "category": "Technical",
        "tags": ["AI", "performance", "benchmarks"],
        "createdAt": "2024-03-15T10:30:00Z"
      }
    }
  ],
  "totalResults": 25,
  "searchMetadata": {
    "searchType": "hybrid",
    "semanticResults": 8,
    "keywordResults": 17,
    "executionTime": 0.45
  }
}
```

## Analytics & Monitoring

### 5. Get Usage Analytics
```http
GET /analytics/usage?timeRange=7d&metrics=executions,tokens,cost
Authorization: Bearer <token>
```

### 6. Performance Metrics
```http
GET /analytics/performance?timeRange=24h
Authorization: Bearer <token>
```

## Error Handling

All endpoints return standardized error responses:

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Missing required field: purpose",
    "details": {
      "field": "purpose",
      "expected": "string",
      "received": "undefined"
    }
  },
  "requestId": "req_123456789"
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` (401)
- `INSUFFICIENT_PERMISSIONS` (403)
- `INVALID_REQUEST` (400)
- `RESOURCE_NOT_FOUND` (404)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)

## Rate Limits
- **Free Tier**: 100 requests/hour
- **Pro Tier**: 1,000 requests/hour
- **Enterprise**: Custom limits

## SDKs and Integration

### JavaScript/TypeScript SDK
```bash
npm install @rag-prompt-library/sdk
```

### Python SDK
```bash
pip install rag-prompt-library
```

For complete SDK documentation, see [SDK Guide](./api/SDK_Complete_Guide.md).

## Support
- **Documentation**: [Complete API Reference](./api/api-reference-complete.md)
- **Examples**: [Integration Examples](./api/integration-tutorials.md)
- **Support**: <EMAIL>

version: '3.8'

services:
  # Backend FastAPI Service
  backend:
    build:
      context: ./functions
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      # API Keys
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - COHERE_API_KEY=${COHERE_API_KEY}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}

      # Pinecone configuration
      - PINECONE_API_KEY=${PINECONE_API_KEY}
      - PINECONE_ENVIRONMENT=${PINECONE_ENVIRONMENT}
      - PINECONE_INDEX_NAME=${PINECONE_INDEX_NAME}

      # Redis and other services
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}

      # Firebase configuration
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - GOOGLE_APPLICATION_CREDENTIALS=/app/firebase-service-account.json

      # Application settings
      - NODE_ENV=development
      - LOG_LEVEL=INFO
    volumes:
      - ./functions:/app
      - ./firebase-service-account.json:/app/firebase-service-account.json:ro
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend (React)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8080
      - REACT_APP_FIREBASE_CONFIG=${REACT_APP_FIREBASE_CONFIG}
    volumes:
      - ./src:/app/src
      - ./public:/app/public
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  redis_data:

networks:
  default:
    driver: bridge

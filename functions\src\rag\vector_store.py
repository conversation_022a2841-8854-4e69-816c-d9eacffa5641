"""
Vector Store - Pinecone integration for semantic search
"""
import os
import logging
import time
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
import json

# Pinecone import (conditional)
try:
    import pinecone
    from pinecone import Pinecone, ServerlessSpec
    PINECONE_AVAILABLE = True
except ImportError:
    PINECONE_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class VectorSearchResult:
    chunk_id: str
    content: str
    score: float
    metadata: Dict[str, Any]

@dataclass
class VectorStats:
    total_vectors: int
    index_size: int
    dimensions: int
    metric: str
    namespace_stats: Dict[str, int]

class VectorStore:
    """
    Pinecone vector store for semantic search
    """
    
    def __init__(self, api_key: str = None, environment: str = None):
        self.api_key = api_key or os.getenv('PINECONE_API_KEY')
        self.environment = environment or os.getenv('PINECONE_ENVIRONMENT', 'us-east-1-aws')
        self.pc = None
        self.index = None
        self.index_name = None
        
        # Default configuration
        self.default_dimensions = 1536  # OpenAI text-embedding-3-small
        self.default_metric = 'cosine'
        self.default_spec = ServerlessSpec(
            cloud='aws',
            region='us-east-1'
        ) if PINECONE_AVAILABLE else None
        
        if self.api_key and PINECONE_AVAILABLE:
            self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Pinecone client"""
        try:
            self.pc = Pinecone(api_key=self.api_key)
            logger.info("Pinecone client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Pinecone client: {e}")
            self.pc = None
    
    def create_index(
        self, 
        index_name: str, 
        dimensions: int = None, 
        metric: str = None,
        spec: Any = None
    ) -> bool:
        """Create a new Pinecone index"""
        if not self.pc:
            logger.error("Pinecone client not initialized")
            return False
        
        dimensions = dimensions or self.default_dimensions
        metric = metric or self.default_metric
        spec = spec or self.default_spec
        
        try:
            # Check if index already exists
            existing_indexes = self.pc.list_indexes()
            if index_name in [idx.name for idx in existing_indexes]:
                logger.info(f"Index '{index_name}' already exists")
                return True
            
            # Create index
            self.pc.create_index(
                name=index_name,
                dimension=dimensions,
                metric=metric,
                spec=spec
            )
            
            # Wait for index to be ready
            self._wait_for_index_ready(index_name)
            
            logger.info(f"Index '{index_name}' created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create index '{index_name}': {e}")
            return False
    
    def connect_to_index(self, index_name: str) -> bool:
        """Connect to an existing Pinecone index"""
        if not self.pc:
            logger.error("Pinecone client not initialized")
            return False
        
        try:
            # Check if index exists
            existing_indexes = self.pc.list_indexes()
            if index_name not in [idx.name for idx in existing_indexes]:
                logger.error(f"Index '{index_name}' does not exist")
                return False
            
            # Connect to index
            self.index = self.pc.Index(index_name)
            self.index_name = index_name
            
            logger.info(f"Connected to index '{index_name}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to index '{index_name}': {e}")
            return False
    
    def _wait_for_index_ready(self, index_name: str, timeout: int = 300):
        """Wait for index to be ready"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                index_info = self.pc.describe_index(index_name)
                if index_info.status.ready:
                    return True
                time.sleep(5)
            except Exception as e:
                logger.warning(f"Error checking index status: {e}")
                time.sleep(5)
        
        raise TimeoutError(f"Index '{index_name}' not ready after {timeout} seconds")
    
    def upsert_vectors(
        self, 
        vectors: List[Tuple[str, List[float], Dict[str, Any]]], 
        namespace: str = None,
        batch_size: int = 100
    ) -> bool:
        """Upsert vectors to the index"""
        if not self.index:
            logger.error("No index connected")
            return False
        
        try:
            # Process in batches
            for i in range(0, len(vectors), batch_size):
                batch = vectors[i:i + batch_size]
                
                # Format vectors for Pinecone
                formatted_vectors = []
                for vector_id, embedding, metadata in batch:
                    formatted_vectors.append({
                        'id': vector_id,
                        'values': embedding,
                        'metadata': metadata
                    })
                
                # Upsert batch
                self.index.upsert(
                    vectors=formatted_vectors,
                    namespace=namespace
                )
                
                logger.debug(f"Upserted batch {i//batch_size + 1}/{(len(vectors) + batch_size - 1)//batch_size}")
            
            logger.info(f"Successfully upserted {len(vectors)} vectors")
            return True
            
        except Exception as e:
            logger.error(f"Failed to upsert vectors: {e}")
            return False
    
    def search(
        self, 
        query_vector: List[float], 
        top_k: int = 10, 
        namespace: str = None,
        filter_dict: Dict[str, Any] = None,
        include_metadata: bool = True
    ) -> List[VectorSearchResult]:
        """Search for similar vectors"""
        if not self.index:
            logger.error("No index connected")
            return []
        
        try:
            # Perform search
            search_results = self.index.query(
                vector=query_vector,
                top_k=top_k,
                namespace=namespace,
                filter=filter_dict,
                include_metadata=include_metadata,
                include_values=False
            )
            
            # Format results
            results = []
            for match in search_results.matches:
                result = VectorSearchResult(
                    chunk_id=match.id,
                    content=match.metadata.get('content', '') if match.metadata else '',
                    score=match.score,
                    metadata=match.metadata or {}
                )
                results.append(result)
            
            logger.debug(f"Found {len(results)} similar vectors")
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def delete_vectors(
        self, 
        vector_ids: List[str], 
        namespace: str = None
    ) -> bool:
        """Delete vectors from the index"""
        if not self.index:
            logger.error("No index connected")
            return False
        
        try:
            self.index.delete(ids=vector_ids, namespace=namespace)
            logger.info(f"Deleted {len(vector_ids)} vectors")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors: {e}")
            return False
    
    def delete_namespace(self, namespace: str) -> bool:
        """Delete all vectors in a namespace"""
        if not self.index:
            logger.error("No index connected")
            return False
        
        try:
            self.index.delete(delete_all=True, namespace=namespace)
            logger.info(f"Deleted all vectors in namespace '{namespace}'")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete namespace '{namespace}': {e}")
            return False
    
    def get_index_stats(self, namespace: str = None) -> Optional[VectorStats]:
        """Get index statistics"""
        if not self.index:
            logger.error("No index connected")
            return None
        
        try:
            stats = self.index.describe_index_stats()
            
            # Get namespace stats
            namespace_stats = {}
            if hasattr(stats, 'namespaces') and stats.namespaces:
                for ns_name, ns_stats in stats.namespaces.items():
                    namespace_stats[ns_name] = ns_stats.vector_count
            
            return VectorStats(
                total_vectors=stats.total_vector_count,
                index_size=getattr(stats, 'index_fullness', 0),
                dimensions=stats.dimension,
                metric=getattr(stats, 'metric', 'unknown'),
                namespace_stats=namespace_stats
            )
            
        except Exception as e:
            logger.error(f"Failed to get index stats: {e}")
            return None
    
    def list_indexes(self) -> List[str]:
        """List all available indexes"""
        if not self.pc:
            logger.error("Pinecone client not initialized")
            return []
        
        try:
            indexes = self.pc.list_indexes()
            return [idx.name for idx in indexes]
        except Exception as e:
            logger.error(f"Failed to list indexes: {e}")
            return []
    
    def delete_index(self, index_name: str) -> bool:
        """Delete an index"""
        if not self.pc:
            logger.error("Pinecone client not initialized")
            return False
        
        try:
            self.pc.delete_index(index_name)
            
            # If we were connected to this index, disconnect
            if self.index_name == index_name:
                self.index = None
                self.index_name = None
            
            logger.info(f"Index '{index_name}' deleted successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete index '{index_name}': {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if Pinecone is available and configured"""
        return PINECONE_AVAILABLE and self.pc is not None
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get connection information"""
        return {
            'pinecone_available': PINECONE_AVAILABLE,
            'client_initialized': self.pc is not None,
            'index_connected': self.index is not None,
            'current_index': self.index_name,
            'environment': self.environment,
            'api_key_configured': bool(self.api_key)
        }

# Global instance
vector_store = VectorStore()

import React from 'react';
import { screen, fireEvent, waitFor, act, cleanup } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ToastProvider, useToast, useSuccessToast, useErrorToast } from '../Toast';
import { renderWithProviders } from '../../../test/test-utils';

// Test component that uses toast hooks
const TestComponent: React.FC = () => {
  const { addToast, clearAllToasts } = useToast();
  const successToast = useSuccessToast();
  const errorToast = useErrorToast();

  return (
    <div>
      <button onClick={() => addToast({ type: 'info', title: 'Info Toast', message: 'Info message' })}>
        Add Info Toast
      </button>
      <button onClick={() => successToast('Success!', 'Success message')}>
        Add Success Toast
      </button>
      <button onClick={() => errorToast('Error!', 'Error message')}>
        Add Error Toast
      </button>
      <button onClick={() => addToast({ 
        type: 'warning', 
        title: 'Warning', 
        message: 'Warning message',
        action: { label: 'Action', onClick: () => console.log('Action clicked') }
      })}>
        Add Warning with Action
      </button>
      <button onClick={clearAllToasts}>
        Clear All
      </button>
    </div>
  );
};

describe('Toast System', () => {
  beforeEach(() => {
    // Don't use fake timers for Toast tests as they interfere with waitFor
    // vi.useFakeTimers();
  });

  afterEach(() => {
    // vi.useRealTimers();
  });

  it('renders toast provider without errors', () => {
    renderWithProviders(
      <ToastProvider>
        <div>Test content</div>
      </ToastProvider>
    );

    expect(screen.getAllByText('Test content')[0]).toBeDefined();
  });

  it('throws error when useToast is used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      renderWithProviders(<TestComponent />);
    }).toThrow('useToast must be used within a ToastProvider');

    consoleSpy.mockRestore();
  });

  it('adds and displays info toast', async () => {
    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Info Toast')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Info Toast')[0]).toBeDefined();
      expect(screen.getAllByText('Info message')[0]).toBeDefined();
    }, { timeout: 1000 }, { timeout: 1000 });
  });

  it('adds and displays success toast', async () => {
    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Success Toast')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Success!')[0]).toBeDefined();
      expect(screen.getAllByText('Success message')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Should have green styling (success) - look for the toast container
    const toastContainer = screen.getAllByText('Success!')[0].closest('[class*="border-l-"]');
    expect(toastContainer).toHaveClass('border-l-green-500');
  });

  it('adds and displays error toast', async () => {
    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Error Toast')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Error!')[0]).toBeDefined();
      expect(screen.getAllByText('Error message')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Should have red styling (error) - look for the toast container
    const toastContainer = screen.getAllByText('Error!')[0].closest('[class*="border-l-"]');
    expect(toastContainer).toHaveClass('border-l-red-500');
  });

  it('displays toast with action button', async () => {
    const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Warning with Action')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Warning')[0]).toBeDefined();
      expect(screen.getAllByText('Action')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Click the action button
    const actionButton = screen.getAllByText('Action')[0];
    act(() => { fireEvent.click(actionButton); });

    expect(consoleSpy).toHaveBeenCalledWith('Action clicked');
    consoleSpy.mockRestore();
  });

  it('removes toast when close button is clicked', async () => {
    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Info Toast')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Info Toast')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Click close button
    const closeButton = screen.getByRole('button', { name: /close/i });
    act(() => { fireEvent.click(closeButton); });

    await waitFor(() => {
      expect(screen.queryByText('Info Toast')).toBeNull();
    }, { timeout: 1000 });
  });

  it.skip('auto-removes toast after duration', async () => {
    vi.useFakeTimers();

    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Success Toast')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Success!')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Fast-forward time to trigger auto-removal (default 5000ms)
    act(() => {
      vi.advanceTimersByTime(5000);
    });

    await waitFor(() => {
      expect(screen.queryByText('Success!')).toBeNull();
    }, { timeout: 1000 });

    vi.useRealTimers();
  });

  it.skip('does not auto-remove error toasts', async () => {
    vi.useFakeTimers();

    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    const addButton = screen.getAllByText('Add Error Toast')[0];
    act(() => { fireEvent.click(addButton); });

    await waitFor(() => {
      expect(screen.getAllByText('Error!')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Fast-forward time - error toasts should not auto-remove
    act(() => {
      vi.advanceTimersByTime(10000);
    });

    // Error toast should still be there
    expect(screen.getAllByText('Error!')[0]).toBeDefined();

    vi.useRealTimers();
  });

  it('clears all toasts', async () => {
    renderWithProviders(
      <ToastProvider>
        <TestComponent />
      </ToastProvider>
    );

    // Add multiple toasts
    fireEvent.click(screen.getAllByText('Add Info Toast')[0]);
    fireEvent.click(screen.getAllByText('Add Success Toast')[0]);
    fireEvent.click(screen.getAllByText('Add Error Toast')[0]);

    await waitFor(() => {
      expect(screen.getAllByText('Info Toast')[0]).toBeDefined();
      expect(screen.getAllByText('Success!')[0]).toBeDefined();
      expect(screen.getAllByText('Error!')[0]).toBeDefined();
    }, { timeout: 1000 });

    // Clear all toasts
    fireEvent.click(screen.getAllByText('Clear All')[0]);

    await waitFor(() => {
      expect(screen.queryByText('Info Toast')).toBeNull();
      expect(screen.queryByText('Success!')).toBeNull();
      expect(screen.queryByText('Error!')).toBeNull();
    }, { timeout: 1000 });
  });

  it('limits number of toasts based on maxToasts prop', async () => {
    const TestComponentWithManyToasts: React.FC = () => {
      const { addToast } = useToast();

      return (
        <div>
          {[1, 2, 3, 4, 5, 6].map(i => (
            <button key={i} onClick={() => addToast({ type: 'info', title: `Toast ${i}` })}>
              Add Toast {i}
            </button>
          ))}
        </div>
      );
    };

    renderWithProviders(
      <ToastProvider maxToasts={3}>
        <TestComponentWithManyToasts />
      </ToastProvider>
    );

    // Add 6 toasts
    for (let i = 1; i <= 6; i++) {
      fireEvent.click(screen.getAllByText(`Add Toast ${i}`)[0]);
    }

    await waitFor(() => {
      // Should only show the last 3 toasts (4, 5, 6)
      expect(screen.queryByText('Toast 1')).toBeNull();
      expect(screen.queryByText('Toast 2')).toBeNull();
      expect(screen.queryByText('Toast 3')).toBeNull();
      expect(screen.getAllByText('Toast 4')[0]).toBeDefined();
      expect(screen.getAllByText('Toast 5')[0]).toBeDefined();
      expect(screen.getAllByText('Toast 6')[0]).toBeDefined();
    }, { timeout: 1000 });
  });

  it('shows correct icons for different toast types', async () => {
    const TestIconComponent: React.FC = () => {
      const { addToast } = useToast();

      return (
        <div>
          <button onClick={() => addToast({ type: 'success', title: 'Success' })}>Success</button>
          <button onClick={() => addToast({ type: 'error', title: 'Error' })}>Error</button>
          <button onClick={() => addToast({ type: 'warning', title: 'Warning' })}>Warning</button>
          <button onClick={() => addToast({ type: 'info', title: 'Info' })}>Info</button>
        </div>
      );
    };

    renderWithProviders(
      <ToastProvider>
        <TestIconComponent />
      </ToastProvider>
    );

    // Add different types of toasts
    fireEvent.click(screen.getAllByText('Success')[0]);
    fireEvent.click(screen.getAllByText('Error')[0]);
    fireEvent.click(screen.getAllByText('Warning')[0]);
    fireEvent.click(screen.getAllByText('Info')[0]);

    await waitFor(() => {
      // Check that all toasts are displayed by looking for toast containers
      const toastContainers = screen.getAllByText('Success');
      expect(toastContainers.length).toBeGreaterThan(1); // Button + Toast

      const errorContainers = screen.getAllByText('Error');
      expect(errorContainers.length).toBeGreaterThan(1); // Button + Toast

      const warningContainers = screen.getAllByText('Warning');
      expect(warningContainers.length).toBeGreaterThan(1); // Button + Toast

      const infoContainers = screen.getAllByText('Info');
      expect(infoContainers.length).toBeGreaterThan(1); // Button + Toast
    }, { timeout: 1000 });

    // Check for specific toast styling by looking for border classes
    expect(document.querySelector('.border-l-green-500')).toBeDefined();
    expect(document.querySelector('.border-l-red-500')).toBeDefined();
    expect(document.querySelector('.border-l-yellow-500')).toBeDefined();
    expect(document.querySelector('.border-l-blue-500')).toBeDefined();
  });
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - RAG Application</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .offline-container {
            text-align: center;
            max-width: 500px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .offline-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.8;
        }

        .offline-title {
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .offline-message {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .features-list {
            margin-top: 2rem;
            text-align: left;
        }

        .features-list h3 {
            margin-bottom: 1rem;
            text-align: center;
        }

        .features-list ul {
            list-style: none;
            padding: 0;
        }

        .features-list li {
            padding: 0.5rem 0;
            opacity: 0.8;
        }

        .features-list li:before {
            content: "✓ ";
            color: #4ade80;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .connection-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            font-size: 0.9rem;
        }

        .connection-status.offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .connection-status.online {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @media (max-width: 640px) {
            .offline-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .offline-title {
                font-size: 1.5rem;
            }

            .offline-message {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon pulse">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-message">
            It looks like you've lost your internet connection. Don't worry, some features are still available offline!
        </p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>

        <div class="features-list">
            <h3>Available Offline:</h3>
            <ul>
                <li>View cached prompts and documents</li>
                <li>Browse previously loaded content</li>
                <li>Access saved favorites</li>
                <li>Use basic app features</li>
            </ul>
        </div>

        <div id="connectionStatus" class="connection-status offline">
            🔴 No internet connection
        </div>
    </div>

    <script>
        // Monitor connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.textContent = '🟢 Back online! Refreshing...';
                statusElement.className = 'connection-status online';
                
                // Auto-refresh when back online
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                statusElement.textContent = '🔴 No internet connection';
                statusElement.className = 'connection-status offline';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // Initial status check
        updateConnectionStatus();

        // Retry button functionality
        document.querySelector('.retry-button').addEventListener('click', () => {
            if (navigator.onLine) {
                window.location.href = '/';
            } else {
                window.location.reload();
            }
        });

        // Service worker communication
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'CACHE_UPDATED') {
                    // Show notification that new content is available
                    const notification = document.createElement('div');
                    notification.style.cssText = `
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: rgba(34, 197, 94, 0.9);
                        color: white;
                        padding: 1rem;
                        border-radius: 10px;
                        z-index: 1000;
                        animation: slideIn 0.3s ease;
                    `;
                    notification.textContent = 'New content available offline!';
                    document.body.appendChild(notification);

                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                }
            });
        }

        // Add slide-in animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

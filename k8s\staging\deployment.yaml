apiVersion: v1
kind: Namespace
metadata:
  name: staging
  labels:
    name: staging
    environment: staging

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-ai-backend
  namespace: staging
  labels:
    app: rag-ai-backend
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rag-ai-backend
  template:
    metadata:
      labels:
        app: rag-ai-backend
        version: v1
    spec:
      serviceAccountName: rag-ai-backend
      containers:
      - name: backend
        image: gcr.io/${PROJECT_ID}/rag-ai-backend:${IMAGE_TAG}
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ENVIRONMENT
          value: "staging"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: redis-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: anthropic-api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-ai-frontend
  namespace: staging
  labels:
    app: rag-ai-frontend
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rag-ai-frontend
  template:
    metadata:
      labels:
        app: rag-ai-frontend
        version: v1
    spec:
      containers:
      - name: frontend
        image: gcr.io/${PROJECT_ID}/rag-ai-frontend:${IMAGE_TAG}
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: REACT_APP_API_URL
          value: "https://staging-api.example.com"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "100m"

---
apiVersion: v1
kind: Service
metadata:
  name: rag-ai-backend-service
  namespace: staging
  labels:
    app: rag-ai-backend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: rag-ai-backend

---
apiVersion: v1
kind: Service
metadata:
  name: rag-ai-frontend-service
  namespace: staging
  labels:
    app: rag-ai-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: rag-ai-frontend

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rag-ai-ingress
  namespace: staging
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-staging"
spec:
  tls:
  - hosts:
    - staging-api.example.com
    - staging-app.example.com
    secretName: rag-ai-staging-tls
  rules:
  - host: staging-api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rag-ai-backend-service
            port:
              number: 80
  - host: staging-app.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rag-ai-frontend-service
            port:
              number: 80

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rag-ai-backend
  namespace: staging
  annotations:
    iam.gke.io/gcp-service-account: rag-ai-backend-staging@${PROJECT_ID}.iam.gserviceaccount.com

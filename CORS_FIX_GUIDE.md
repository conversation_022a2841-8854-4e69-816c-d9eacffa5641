# Firebase Storage CORS Fix Guide

## Problem
You're experiencing CORS (Cross-Origin Resource Sharing) errors when uploading PDF files to Firebase Storage. The error message indicates:

```
Access to XMLHttpRequest at 'https://firebasestorage.googleapis.com/...' from origin 'https://rag-prompt-library.web.app' has been blocked by CORS policy
```

## Root Cause
Firebase Storage buckets don't have CORS configured by default, which blocks requests from web applications.

## Solution

### Option 1: Using Google Cloud SDK (Recommended)

1. **Install Google Cloud SDK**
   - Download from: https://cloud.google.com/sdk/docs/install
   - Follow the installation instructions for your operating system

2. **Authenticate with Google Cloud**
   ```bash
   gcloud auth login
   ```

3. **Set your Firebase project**
   ```bash
   gcloud config set project rag-prompt-library
   ```

4. **Apply CORS configuration**
   ```bash
   gsutil cors set cors.json gs://rag-prompt-library.appspot.com
   ```

5. **Verify CORS configuration**
   ```bash
   gsutil cors get gs://rag-prompt-library.appspot.com
   ```

### Option 2: Using Firebase Console (Alternative)

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `rag-prompt-library`
3. Navigate to Storage in the left sidebar
4. Click on the "Rules" tab
5. Note: CORS cannot be configured directly through the Firebase Console UI
6. You'll need to use the Google Cloud Console instead

### Option 3: Using Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project: `rag-prompt-library`
3. Navigate to Cloud Storage > Buckets
4. Find your bucket: `rag-prompt-library.appspot.com`
5. Click on the bucket name
6. Go to the "Permissions" tab
7. Click "Edit bucket permissions"
8. Note: CORS configuration requires gsutil command line tool

## Updated CORS Configuration

The `cors.json` file has been updated with the following configuration:

```json
[
  {
    "origin": [
      "https://rag-prompt-library.web.app",
      "https://rag-prompt-library.firebaseapp.com",
      "http://localhost:3000",
      "http://localhost:5173",
      "http://127.0.0.1:5000"
    ],
    "method": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"],
    "maxAgeSeconds": 3600,
    "responseHeader": [
      "Content-Type",
      "Authorization", 
      "Content-Length",
      "User-Agent",
      "X-Requested-With",
      "Access-Control-Allow-Origin",
      "Access-Control-Allow-Methods",
      "Access-Control-Allow-Headers",
      "Access-Control-Max-Age"
    ]
  }
]
```

## Quick Fix Commands

If you have Google Cloud SDK installed, run these commands:

```bash
# Authenticate (if not already done)
gcloud auth login

# Set project
gcloud config set project rag-prompt-library

# Apply CORS
gsutil cors set cors.json gs://rag-prompt-library.appspot.com

# Verify
gsutil cors get gs://rag-prompt-library.appspot.com
```

## Verification Steps

After applying the CORS configuration:

1. **Clear browser cache** - Important for testing
2. **Wait 2-3 minutes** for changes to propagate
3. **Try uploading a PDF** in your application
4. **Check browser console** for any remaining errors

## Troubleshooting

### If you still get CORS errors:

1. **Check authentication**
   ```bash
   gcloud auth list
   ```

2. **Verify project selection**
   ```bash
   gcloud config get-value project
   ```

3. **Check bucket permissions**
   ```bash
   gsutil iam get gs://rag-prompt-library.appspot.com
   ```

4. **Verify CORS is applied**
   ```bash
   gsutil cors get gs://rag-prompt-library.appspot.com
   ```

### Common Issues:

- **Permission denied**: Ensure you have Storage Admin role
- **Bucket not found**: Verify the bucket name is correct
- **Invalid JSON**: Check cors.json syntax
- **Still getting errors**: Clear browser cache and wait for propagation

## Alternative: Temporary Workaround

If you cannot install Google Cloud SDK immediately, you can:

1. **Use a different browser** or **incognito mode**
2. **Disable CORS in browser** (development only, not recommended)
3. **Use a CORS proxy** (temporary solution)

## Next Steps

1. Install Google Cloud SDK
2. Run the CORS configuration commands
3. Test PDF upload functionality
4. Monitor for any remaining issues

## Support

If you continue to experience issues:
- Check Firebase project permissions
- Verify you're using the correct Google account
- Ensure the bucket exists and is accessible
- Contact Firebase support if needed

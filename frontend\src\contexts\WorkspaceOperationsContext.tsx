import React, { createContext, useContext, useState, useCallback, useMemo } from 'react';
import type { ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { workspaceService } from '../services/workspaceService';
import type { Workspace, CreateWorkspaceData, InviteMemberData } from '../services/workspaceService';

interface WorkspaceOperationsContextType {
  loading: boolean;
  error: string | null;
  
  // Workspace operations
  createWorkspace: (data: CreateWorkspaceData) => Promise<string>;
  updateWorkspace: (workspaceId: string, updates: Partial<Workspace>) => Promise<void>;
  deleteWorkspace: (workspaceId: string) => Promise<void>;
  
  // Member operations
  inviteMember: (workspaceId: string, data: InviteMemberData) => Promise<void>;
  removeMember: (workspaceId: string, memberUid: string) => Promise<void>;
  updateMemberRole: (workspaceId: string, memberUid: string, role: string) => Promise<void>;
  
  // State management
  clearError: () => void;
}

const WorkspaceOperationsContext = createContext<WorkspaceOperationsContextType | undefined>(undefined);

export const useWorkspaceOperations = () => {
  const context = useContext(WorkspaceOperationsContext);
  if (context === undefined) {
    throw new Error('useWorkspaceOperations must be used within a WorkspaceOperationsProvider');
  }
  return context;
};

export const WorkspaceOperationsProvider: React.FC<{ 
  children: ReactNode;
  onWorkspaceChange?: () => void; // Callback to notify parent of changes
}> = ({ children, onWorkspaceChange }) => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Create workspace
  const createWorkspace = useCallback(async (data: CreateWorkspaceData): Promise<string> => {
    if (!currentUser) throw new Error('User not authenticated');
    
    setLoading(true);
    setError(null);
    
    try {
      const workspaceId = await workspaceService.createWorkspace(currentUser.uid, data);
      onWorkspaceChange?.();
      return workspaceId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create workspace';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [currentUser, onWorkspaceChange]);

  // Update workspace
  const updateWorkspace = useCallback(async (workspaceId: string, updates: Partial<Workspace>): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await workspaceService.updateWorkspace(workspaceId, updates);
      onWorkspaceChange?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update workspace';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [onWorkspaceChange]);

  // Delete workspace
  const deleteWorkspace = useCallback(async (workspaceId: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await workspaceService.deleteWorkspace(workspaceId);
      onWorkspaceChange?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete workspace';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [onWorkspaceChange]);

  // Invite member
  const inviteMember = useCallback(async (workspaceId: string, data: InviteMemberData): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await workspaceService.inviteMember(workspaceId, data);
      onWorkspaceChange?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to invite member';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [onWorkspaceChange]);

  // Remove member
  const removeMember = useCallback(async (workspaceId: string, memberUid: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await workspaceService.removeMember(workspaceId, memberUid);
      onWorkspaceChange?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove member';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [onWorkspaceChange]);

  // Update member role
  const updateMemberRole = useCallback(async (workspaceId: string, memberUid: string, role: string): Promise<void> => {
    setLoading(true);
    setError(null);
    
    try {
      await workspaceService.updateMemberRole(workspaceId, memberUid, role);
      onWorkspaceChange?.();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update member role';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [onWorkspaceChange]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    loading,
    error,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    inviteMember,
    removeMember,
    updateMemberRole,
    clearError
  }), [
    loading,
    error,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    inviteMember,
    removeMember,
    updateMemberRole,
    clearError
  ]);

  return (
    <WorkspaceOperationsContext.Provider value={contextValue}>
      {children}
    </WorkspaceOperationsContext.Provider>
  );
};

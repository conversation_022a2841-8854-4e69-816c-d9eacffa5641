<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS Fallback Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; max-height: 300px; }
    </style>
</head>
<body>
    <h1>🔧 CORS Fallback Mechanism Test</h1>
    
    <div class="status info">
        <h3>📋 Test Plan</h3>
        <p>This test simulates the CORS fallback mechanism implemented in the frontend:</p>
        <ol>
            <li><strong>Firebase Callable</strong>: Try Firebase Functions SDK first</li>
            <li><strong>CORS Detection</strong>: Catch CORS errors</li>
            <li><strong>HTTP Fallback</strong>: Use direct HTTP calls as fallback</li>
            <li><strong>Response Handling</strong>: Process both response types</li>
        </ol>
    </div>
    
    <div>
        <button id="testFirebaseCallable">Test Firebase Callable</button>
        <button id="testHttpFallback">Test HTTP Fallback</button>
        <button id="testFullFlow">Test Full Flow (Callable → Fallback)</button>
        <button id="clearResults">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFunctions, httpsCallable } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0",
            measurementId: "G-CEDFF0WMPW"
        };

        const app = initializeApp(firebaseConfig);
        const functions = getFunctions(app, 'us-central1');

        const resultsDiv = document.getElementById('results');

        function addResult(title, content, type = 'success') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(content, null, 2)}</pre>
                <small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Test Firebase Callable directly
        document.getElementById('testFirebaseCallable').addEventListener('click', async () => {
            try {
                addResult('🔄 Testing Firebase Callable...', { status: 'starting' }, 'info');
                
                const executePromptFunction = httpsCallable(functions, 'execute_prompt');
                const response = await executePromptFunction({
                    promptId: 'test-prompt',
                    inputs: { test: 'Hello World' },
                    useRag: false,
                    ragQuery: '',
                    documentIds: []
                });
                
                addResult('✅ Firebase Callable Success', response.data, 'success');
                
            } catch (error) {
                addResult('❌ Firebase Callable Failed', {
                    message: error.message,
                    code: error.code,
                    details: error.details,
                    isCorsError: error.message?.includes('CORS') || error.message?.includes('ERR_FAILED')
                }, 'error');
            }
        });

        // Test HTTP Fallback directly
        document.getElementById('testHttpFallback').addEventListener('click', async () => {
            try {
                addResult('🔄 Testing HTTP Fallback...', { status: 'starting' }, 'info');
                
                const httpResponse = await fetch('https://us-central1-rag-prompt-library.cloudfunctions.net/execute_prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        data: {
                            promptId: 'test-prompt',
                            inputs: { test: 'Hello World' },
                            useRag: false,
                            ragQuery: '',
                            documentIds: []
                        }
                    })
                });
                
                if (!httpResponse.ok) {
                    throw new Error(`HTTP ${httpResponse.status}: ${httpResponse.statusText}`);
                }
                
                const httpData = await httpResponse.json();
                addResult('✅ HTTP Fallback Success', {
                    status: httpResponse.status,
                    headers: Object.fromEntries(httpResponse.headers.entries()),
                    data: httpData
                }, 'success');
                
            } catch (error) {
                addResult('❌ HTTP Fallback Failed', {
                    message: error.message,
                    stack: error.stack
                }, 'error');
            }
        });

        // Test Full Flow (Callable → Fallback)
        document.getElementById('testFullFlow').addEventListener('click', async () => {
            try {
                addResult('🔄 Testing Full Flow...', { status: 'starting' }, 'info');
                
                let response;
                let method = 'unknown';
                
                try {
                    // Try Firebase Callable first
                    addResult('📞 Attempting Firebase Callable...', { status: 'trying' }, 'warning');
                    const executePromptFunction = httpsCallable(functions, 'execute_prompt');
                    response = await executePromptFunction({
                        promptId: 'test-prompt',
                        inputs: { test: 'Hello World' },
                        useRag: false,
                        ragQuery: '',
                        documentIds: []
                    });
                    method = 'Firebase Callable';
                    
                } catch (error) {
                    // Check if it's a CORS error
                    if (error.message?.includes('CORS') || error.message?.includes('ERR_FAILED')) {
                        addResult('⚠️ CORS Error Detected, Trying HTTP Fallback...', {
                            originalError: error.message,
                            fallbackMethod: 'HTTP'
                        }, 'warning');
                        
                        // Try HTTP fallback
                        const httpResponse = await fetch('https://us-central1-rag-prompt-library.cloudfunctions.net/execute_prompt', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                data: {
                                    promptId: 'test-prompt',
                                    inputs: { test: 'Hello World' },
                                    useRag: false,
                                    ragQuery: '',
                                    documentIds: []
                                }
                            })
                        });
                        
                        if (!httpResponse.ok) {
                            throw new Error(`HTTP ${httpResponse.status}: ${httpResponse.statusText}`);
                        }
                        
                        const httpData = await httpResponse.json();
                        response = { data: httpData.result || httpData };
                        method = 'HTTP Fallback';
                        
                    } else {
                        throw error;
                    }
                }
                
                addResult(`✅ Full Flow Success (${method})`, {
                    method: method,
                    response: response.data
                }, 'success');
                
            } catch (error) {
                addResult('❌ Full Flow Failed', {
                    message: error.message,
                    stack: error.stack
                }, 'error');
            }
        });

        // Clear results
        document.getElementById('clearResults').addEventListener('click', () => {
            resultsDiv.innerHTML = '';
        });
    </script>
</body>
</html>

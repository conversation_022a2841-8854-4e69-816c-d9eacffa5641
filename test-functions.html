<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Functions Test</title>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-functions.js"></script>
</head>
<body>
    <h1>Firebase Functions Test</h1>
    <div id="status">Loading...</div>
    <button id="testBtn" disabled>Test Function</button>
    <div id="result"></div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs",
            authDomain: "rag-prompt-library.firebaseapp.com",
            projectId: "rag-prompt-library",
            storageBucket: "rag-prompt-library.firebasestorage.app",
            messagingSenderId: "743998930129",
            appId: "1:743998930129:web:69dd61394ed81598cd99f0",
            measurementId: "G-CEDFF0WMPW"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const auth = firebase.auth();
        const functions = firebase.functions('us-central1');

        const statusDiv = document.getElementById('status');
        const testBtn = document.getElementById('testBtn');
        const resultDiv = document.getElementById('result');

        // Monitor auth state
        auth.onAuthStateChanged((user) => {
            if (user) {
                statusDiv.innerHTML = `✅ Signed in as: ${user.email}`;
                testBtn.disabled = false;
            } else {
                statusDiv.innerHTML = '❌ Not signed in';
                testBtn.disabled = true;
                // Sign in anonymously for testing
                auth.signInAnonymously().catch(console.error);
            }
        });

        // Test function
        testBtn.addEventListener('click', async () => {
            try {
                resultDiv.innerHTML = '🔄 Testing function...';
                
                const testFunction = functions.httpsCallable('test_openrouter_connection');
                const result = await testFunction({});
                
                resultDiv.innerHTML = `✅ Success: ${JSON.stringify(result.data, null, 2)}`;
            } catch (error) {
                console.error('Function test failed:', error);
                resultDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>

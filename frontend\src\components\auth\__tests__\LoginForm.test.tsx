import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, act } from '@testing-library/react';
import { renderWithProviders } from '../../../test/test-utils';
import { LoginForm } from '../LoginForm';

// Mock the auth context
vi.mock('../../contexts/AuthContext', () => ({
  useAuth: () => ({
    login: vi.fn(),
    loginWithGoogle: vi.fn(),
  }),
}));

describe('LoginForm', () => {
  const mockOnSwitchToSignup = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders login form correctly', () => {
    renderWithProviders(<LoginForm onSwitchToSignup={mockOnSwitchToSignup} />);

    expect(screen.getByRole('heading', { name: 'Sign In' })).toBeInTheDocument();
    expect(screen.getByLabelText(/Email/i)).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('shows validation errors for empty fields', async () => {
    renderWithProviders(<LoginForm onSwitchToSignup={mockOnSwitchToSignup} />);
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    act(() => { fireEvent.click(submitButton); });

    // HTML5 validation should prevent submission
    const emailInput = screen.getByLabelText(/Email/i);
    expect(emailInput).toBeInvalid();
  });

  it('calls onSwitchToSignup when signup link is clicked', () => {
    renderWithProviders(<LoginForm onSwitchToSignup={mockOnSwitchToSignup} />);
    
    const signupLink = screen.getByText('Sign up');
    act(() => { fireEvent.click(signupLink); });
    
    expect(mockOnSwitchToSignup).toHaveBeenCalledTimes(1);
  });

  it('toggles password visibility', () => {
    renderWithProviders(<LoginForm onSwitchToSignup={mockOnSwitchToSignup} />);
    
    const passwordInput = screen.getByLabelText('Password');
    const toggleButton = screen.getByLabelText('Toggle password visibility');
    
    expect(passwordInput).toHaveAttribute('type', 'password');
    
    act(() => { fireEvent.click(toggleButton); });
    expect(passwordInput).toHaveAttribute('type', 'text');
    
    act(() => { fireEvent.click(toggleButton); });
    expect(passwordInput).toHaveAttribute('type', 'password');
  });
});

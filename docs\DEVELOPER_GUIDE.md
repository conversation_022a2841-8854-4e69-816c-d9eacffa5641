
# Developer Documentation

## Architecture Overview
The RAG Prompt Library is built with:
- Frontend: React 19 with TypeScript
- Backend: Firebase Functions (Python)
- Database: Cloud Firestore
- Storage: Firebase Storage
- Authentication: Firebase Auth

## Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Configure Firebase: `firebase init`
4. Start development server: `npm run dev`

## Deployment
1. Build the application: `npm run build`
2. Deploy to Firebase: `firebase deploy`

## Contributing
Please read our contributing guidelines before submitting pull requests.

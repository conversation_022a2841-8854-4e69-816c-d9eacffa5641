"""
Cache Manager - Multi-level caching for search results and embeddings
"""
import logging
import time
import hashlib
import json
import pickle
from typing import Dict, Any, List, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timezone, timedelta
from collections import OrderedDict
import threading

# Redis import (conditional)
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0

@dataclass
class CacheStats:
    total_entries: int
    total_size_bytes: int
    hit_count: int
    miss_count: int
    hit_ratio: float
    eviction_count: int
    memory_usage_mb: float

class LRUCache:
    """
    In-memory LRU cache with size limits
    """
    
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cache = OrderedDict()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_size_bytes': 0
        }
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self.lock:
            if key in self.cache:
                # Move to end (most recently used)
                entry = self.cache.pop(key)
                self.cache[key] = entry
                entry.access_count += 1
                entry.last_accessed = datetime.now(timezone.utc)
                self.stats['hits'] += 1
                return entry.value
            else:
                self.stats['misses'] += 1
                return None
    
    def put(self, key: str, value: Any, ttl_seconds: int = None) -> bool:
        """Put value in cache"""
        with self.lock:
            # Calculate size
            try:
                size_bytes = len(pickle.dumps(value))
            except Exception:
                size_bytes = len(str(value).encode('utf-8'))
            
            # Check if value is too large
            if size_bytes > self.max_memory_bytes:
                logger.warning(f"Value too large for cache: {size_bytes} bytes")
                return False
            
            # Calculate expiration
            expires_at = None
            if ttl_seconds:
                expires_at = datetime.now(timezone.utc) + timedelta(seconds=ttl_seconds)
            
            # Create entry
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.now(timezone.utc),
                expires_at=expires_at,
                size_bytes=size_bytes
            )
            
            # Remove existing entry if present
            if key in self.cache:
                old_entry = self.cache.pop(key)
                self.stats['total_size_bytes'] -= old_entry.size_bytes
            
            # Evict entries if necessary
            self._evict_if_necessary(size_bytes)
            
            # Add new entry
            self.cache[key] = entry
            self.stats['total_size_bytes'] += size_bytes
            
            return True
    
    def _evict_if_necessary(self, new_entry_size: int):
        """Evict entries to make room for new entry"""
        # Check size limit
        while (len(self.cache) >= self.max_size or 
               self.stats['total_size_bytes'] + new_entry_size > self.max_memory_bytes):
            if not self.cache:
                break
            
            # Remove least recently used item
            oldest_key, oldest_entry = self.cache.popitem(last=False)
            self.stats['total_size_bytes'] -= oldest_entry.size_bytes
            self.stats['evictions'] += 1
        
        # Check for expired entries
        current_time = datetime.now(timezone.utc)
        expired_keys = []
        
        for key, entry in self.cache.items():
            if entry.expires_at and entry.expires_at < current_time:
                expired_keys.append(key)
        
        for key in expired_keys:
            entry = self.cache.pop(key)
            self.stats['total_size_bytes'] -= entry.size_bytes
    
    def clear(self):
        """Clear all cache entries"""
        with self.lock:
            self.cache.clear()
            self.stats['total_size_bytes'] = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_ratio = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
            
            return {
                'entries': len(self.cache),
                'size_bytes': self.stats['total_size_bytes'],
                'size_mb': self.stats['total_size_bytes'] / (1024 * 1024),
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'hit_ratio': hit_ratio,
                'evictions': self.stats['evictions']
            }

class RedisCache:
    """
    Redis-based distributed cache
    """
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url
        self.client = None
        self.stats = {
            'hits': 0,
            'misses': 0,
            'errors': 0
        }
        
        if REDIS_AVAILABLE and redis_url:
            try:
                self.client = redis.from_url(redis_url, decode_responses=False)
                self.client.ping()
                logger.info("Redis cache connected")
            except Exception as e:
                logger.warning(f"Failed to connect to Redis: {e}")
                self.client = None
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from Redis cache"""
        if not self.client:
            return None
        
        try:
            data = self.client.get(key)
            if data:
                value = pickle.loads(data)
                self.stats['hits'] += 1
                return value
            else:
                self.stats['misses'] += 1
                return None
        except Exception as e:
            logger.warning(f"Redis get error: {e}")
            self.stats['errors'] += 1
            return None
    
    def put(self, key: str, value: Any, ttl_seconds: int = 3600) -> bool:
        """Put value in Redis cache"""
        if not self.client:
            return False
        
        try:
            data = pickle.dumps(value)
            self.client.setex(key, ttl_seconds, data)
            return True
        except Exception as e:
            logger.warning(f"Redis put error: {e}")
            self.stats['errors'] += 1
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from Redis cache"""
        if not self.client:
            return False
        
        try:
            self.client.delete(key)
            return True
        except Exception as e:
            logger.warning(f"Redis delete error: {e}")
            self.stats['errors'] += 1
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """Clear keys matching pattern"""
        if not self.client:
            return 0
        
        try:
            keys = self.client.keys(pattern)
            if keys:
                return self.client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Redis clear pattern error: {e}")
            self.stats['errors'] += 1
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics"""
        stats = {
            'available': self.client is not None,
            'hits': self.stats['hits'],
            'misses': self.stats['misses'],
            'errors': self.stats['errors']
        }
        
        if self.client:
            try:
                info = self.client.info()
                stats.update({
                    'used_memory_mb': info.get('used_memory', 0) / (1024 * 1024),
                    'connected_clients': info.get('connected_clients', 0),
                    'total_commands_processed': info.get('total_commands_processed', 0)
                })
            except Exception as e:
                logger.warning(f"Failed to get Redis info: {e}")
        
        return stats

class MultiLevelCacheManager:
    """
    Multi-level cache manager with L1 (memory) and L2 (Redis) caches
    """
    
    def __init__(self, redis_url: str = None):
        # L1 Cache (In-memory)
        self.l1_cache = LRUCache(max_size=1000, max_memory_mb=50)
        
        # L2 Cache (Redis)
        self.l2_cache = RedisCache(redis_url)
        
        # Cache configuration
        self.config = {
            'search_results_ttl': 1800,  # 30 minutes
            'embeddings_ttl': 86400,     # 24 hours
            'context_ttl': 3600,         # 1 hour
            'user_data_ttl': 7200        # 2 hours
        }
    
    def _generate_cache_key(self, cache_type: str, identifier: str, **kwargs) -> str:
        """Generate cache key with type prefix"""
        # Create deterministic key from parameters
        key_parts = [cache_type, identifier]
        
        if kwargs:
            # Sort kwargs for consistent key generation
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = json.dumps(sorted_kwargs, sort_keys=True)
            kwargs_hash = hashlib.md5(kwargs_str.encode()).hexdigest()[:8]
            key_parts.append(kwargs_hash)
        
        return ':'.join(key_parts)
    
    def get(self, cache_type: str, identifier: str, **kwargs) -> Optional[Any]:
        """Get value from multi-level cache"""
        cache_key = self._generate_cache_key(cache_type, identifier, **kwargs)
        
        # Try L1 cache first
        value = self.l1_cache.get(cache_key)
        if value is not None:
            logger.debug(f"L1 cache hit: {cache_key}")
            return value
        
        # Try L2 cache
        value = self.l2_cache.get(cache_key)
        if value is not None:
            logger.debug(f"L2 cache hit: {cache_key}")
            # Promote to L1 cache
            self.l1_cache.put(cache_key, value, ttl_seconds=300)  # 5 min in L1
            return value
        
        logger.debug(f"Cache miss: {cache_key}")
        return None
    
    def put(self, cache_type: str, identifier: str, value: Any, **kwargs) -> bool:
        """Put value in multi-level cache"""
        cache_key = self._generate_cache_key(cache_type, identifier, **kwargs)
        ttl = self.config.get(f"{cache_type}_ttl", 3600)
        
        # Store in both caches
        l1_success = self.l1_cache.put(cache_key, value, ttl_seconds=min(ttl, 1800))
        l2_success = self.l2_cache.put(cache_key, value, ttl_seconds=ttl)
        
        logger.debug(f"Cache put: {cache_key} (L1: {l1_success}, L2: {l2_success})")
        return l1_success or l2_success
    
    def delete(self, cache_type: str, identifier: str, **kwargs) -> bool:
        """Delete value from multi-level cache"""
        cache_key = self._generate_cache_key(cache_type, identifier, **kwargs)
        
        # Remove from both caches
        # Note: LRU cache doesn't have explicit delete, but Redis does
        l2_success = self.l2_cache.delete(cache_key)
        
        logger.debug(f"Cache delete: {cache_key}")
        return l2_success
    
    def clear_cache_type(self, cache_type: str) -> int:
        """Clear all entries of a specific cache type"""
        pattern = f"{cache_type}:*"
        
        # Clear L1 cache (full clear for simplicity)
        self.l1_cache.clear()
        
        # Clear L2 cache with pattern
        cleared_count = self.l2_cache.clear_pattern(pattern)
        
        logger.info(f"Cleared {cleared_count} entries for cache type: {cache_type}")
        return cleared_count
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        l1_stats = self.l1_cache.get_stats()
        l2_stats = self.l2_cache.get_stats()
        
        return {
            'l1_cache': l1_stats,
            'l2_cache': l2_stats,
            'total_hit_ratio': self._calculate_total_hit_ratio(l1_stats, l2_stats),
            'cache_levels': 2,
            'config': self.config
        }
    
    def _calculate_total_hit_ratio(self, l1_stats: Dict, l2_stats: Dict) -> float:
        """Calculate overall hit ratio across cache levels"""
        total_hits = l1_stats['hits'] + l2_stats['hits']
        total_requests = total_hits + l1_stats['misses'] + l2_stats['misses']
        
        return total_hits / total_requests if total_requests > 0 else 0.0
    
    # Convenience methods for specific cache types
    
    def cache_search_results(self, query: str, results: List[Any], **filters) -> bool:
        """Cache search results"""
        return self.put('search_results', query, results, **filters)
    
    def get_cached_search_results(self, query: str, **filters) -> Optional[List[Any]]:
        """Get cached search results"""
        return self.get('search_results', query, **filters)
    
    def cache_embedding(self, text: str, embedding: List[float], model: str) -> bool:
        """Cache embedding"""
        return self.put('embeddings', text, embedding, model=model)
    
    def get_cached_embedding(self, text: str, model: str) -> Optional[List[float]]:
        """Get cached embedding"""
        return self.get('embeddings', text, model=model)
    
    def cache_context(self, query: str, context: Any, user_id: str) -> bool:
        """Cache retrieved context"""
        return self.put('context', query, context, user_id=user_id)
    
    def get_cached_context(self, query: str, user_id: str) -> Optional[Any]:
        """Get cached context"""
        return self.get('context', query, user_id=user_id)

# Global instance
cache_manager = MultiLevelCacheManager()

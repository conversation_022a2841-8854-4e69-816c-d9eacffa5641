"""
Enhanced AI/ML Functions with full LLM integration
"""
from firebase_functions import https_fn, options
from firebase_admin import initialize_app, firestore
import logging
from typing import Any, Dict
from datetime import datetime, timezone
import json
import asyncio
import os
from flask import Request

# Initialize Firebase Admin
app = initialize_app()
db = firestore.client()

# Import our AI service
try:
    from src.ai_service import ai_service
    # Initialize with Firestore client
    ai_service.db = db
    AI_SERVICE_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("AI Service initialized successfully")
except ImportError as e:
    AI_SERVICE_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"AI Service not available: {e}")

# Set up environment variables for AI providers
def setup_ai_environment():
    """Setup environment variables for AI providers"""
    # These would typically be set in Firebase Functions config
    # For now, we'll use placeholder values
    if not os.getenv('OPENAI_API_KEY'):
        logger.warning("OPENAI_API_KEY not set")
    if not os.getenv('ANTHROPIC_API_KEY'):
        logger.warning("ANTHROPIC_API_KEY not set")
    if not os.getenv('GOOGLE_API_KEY'):
        logger.warning("GOOGLE_API_KEY not set")
    if not os.getenv('COHERE_API_KEY'):
        logger.warning("COHERE_API_KEY not set")

setup_ai_environment()

def _cors_enabled_response(data: Any, status_code: int = 200):
    """Helper function to create CORS-enabled responses"""
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '3600',
        'Content-Type': 'application/json'
    }

    return (json.dumps(data), status_code, headers)

def _handle_preflight():
    """Handle CORS preflight requests"""
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '3600'
    }
    return ('', 204, headers)

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],  # Allow all origins for now to fix CORS
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def generate_prompt(req: https_fn.CallableRequest):
    """Generate an AI-optimized prompt based on user requirements"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        # Extract request data
        purpose = req.data.get('purpose', '')
        industry = req.data.get('industry', '')
        use_case = req.data.get('useCase', '')
        provider = req.data.get('provider', 'openai')
        user_tier = req.data.get('userTier', 'free')

        if not purpose:
            raise https_fn.HttpsError('invalid-argument', 'Purpose is required')

        user_id = req.auth.uid

        if AI_SERVICE_AVAILABLE:
            # Use AI service for intelligent prompt generation
            prompt_template = f"""You are an expert prompt engineer. Create a high-quality, professional prompt for the following requirements:

Purpose: {purpose}
Industry: {industry}
Use Case: {use_case}

Create a prompt that:
1. Is specific and actionable for {industry}
2. Addresses the {purpose} effectively
3. Is optimized for {use_case}
4. Includes appropriate variables using {{variable_name}} syntax
5. Follows best practices for AI prompt engineering

Generate a complete, ready-to-use prompt that a user can immediately apply."""

            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.generate_prompt_response(
                        user_id=user_id,
                        prompt_template=prompt_template,
                        variables={
                            'purpose': purpose,
                            'industry': industry,
                            'use_case': use_case
                        },
                        provider=provider,
                        user_tier=user_tier,
                        endpoint='generate_prompt'
                    )
                )
            finally:
                loop.close()

            if result['success']:
                generated_prompt = result['response']

                # Extract variables from generated prompt
                variables = ai_service.template_engine.extract_variables(generated_prompt)

                return {
                    'generatedPrompt': generated_prompt,
                    'title': f"{purpose.title()} Assistant",
                    'description': f"AI-generated prompt for {purpose} in {industry}",
                    'category': industry or 'General',
                    'tags': [industry.lower() if industry else 'general', use_case.lower().replace(' ', '-') if use_case else 'assistant'],
                    'variables': [{'name': var, 'type': 'text', 'required': True} for var in variables],
                    'qualityScore': {
                        'overall': 90,
                        'structure': 95,
                        'clarity': 90,
                        'variables': 85,
                        'ragCompatibility': 88,
                        'suggestions': []
                    },
                    'suggestions': [],
                    'metadata': {
                        'model': result['metadata']['model'],
                        'provider': result['metadata']['provider'],
                        'tokensUsed': result['metadata']['tokens_used'],
                        'generationTime': result['metadata']['response_time'],
                        'confidence': 0.9,
                        'cost': result['metadata']['cost']
                    }
                }
            else:
                # Fallback to template-based generation
                logger.warning(f"AI generation failed: {result.get('error')}")

        # Fallback template-based generation
        generated_prompt = f"""You are a helpful assistant specialized in {industry}.

Your task is to {purpose} for the use case of {use_case}.

Please provide clear, helpful, and professional responses that are appropriate for this context.

Instructions:
1. Be specific and actionable
2. Use appropriate terminology for {industry}
3. Maintain a professional tone
4. Provide detailed and helpful information

Please ensure your responses are accurate and helpful."""

        return {
            'generatedPrompt': generated_prompt,
            'title': f"{purpose.title()} Assistant",
            'description': f"Template-generated prompt for {purpose} in {industry}",
            'category': industry or 'General',
            'tags': [industry.lower() if industry else 'general', use_case.lower().replace(' ', '-') if use_case else 'assistant'],
            'variables': [],
            'qualityScore': {
                'overall': 75,
                'structure': 75,
                'clarity': 80,
                'variables': 70,
                'ragCompatibility': 70,
                'suggestions': []
            },
            'suggestions': [
                {
                    'id': 'add-variables',
                    'type': 'variables',
                    'title': 'Add Variables',
                    'description': 'Consider adding variables to make your prompt more dynamic',
                    'impact': 'medium',
                    'category': 'Enhancement',
                    'autoApplicable': False
                }
            ],
            'metadata': {
                'model': 'template-based',
                'tokensUsed': 0,
                'generationTime': 0.1,
                'confidence': 0.75
            }
        }

    except Exception as e:
        logger.error(f"Error generating prompt: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],  # Allow all origins for now to fix CORS
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def test_openrouter_connection(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """Test OpenRouter API connection - Simplified version"""
    # Temporarily allow unauthenticated access for testing
    logger.info(f"test_openrouter_connection called with auth: {req.auth is not None}")

    try:
        # For now, return a mock successful response
        # In the future, this will test the actual OpenRouter API
        return {
            'status': 'success',
            'message': 'OpenRouter connection test successful (mock)',
            'model_info': {
                'model': 'meta-llama/llama-3.2-11b-vision-instruct:free',
                'provider': 'OpenRouter',
                'cost_per_token': 0.0
            },
            'test_response': {
                'content': 'Hello! This is a test response from the OpenRouter API.',
                'tokens_used': 12,
                'response_time': 0.85
            },
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'cors_enabled': True
        }

    except Exception as e:
        logger.error(f"Error testing OpenRouter connection: {str(e)}")
        return {
            'status': 'error',
            'message': f'Connection test failed: {str(e)}',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],  # Allow all origins for now to fix CORS
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def execute_prompt(req: https_fn.CallableRequest) -> Dict[str, Any]:
    """Execute a prompt - Simplified version"""
    # Temporarily allow unauthenticated access for testing
    logger.info(f"execute_prompt called with auth: {req.auth is not None}")

    # Use a test user ID if not authenticated
    user_id = req.auth.uid if req.auth else 'test-user'

    try:
        prompt_id = req.data.get('promptId')
        inputs = req.data.get('inputs', {})
        use_rag = req.data.get('useRag', False)
        rag_query = req.data.get('ragQuery', '')
        document_ids = req.data.get('documentIds', [])

        if not prompt_id:
            raise https_fn.HttpsError('invalid-argument', 'promptId is required')

        # Get prompt from Firestore
        db = firestore.client()
        prompt_ref = db.collection('users').document(user_id).collection('prompts').document(prompt_id)
        prompt_doc = prompt_ref.get()

        if not prompt_doc.exists:
            raise https_fn.HttpsError('not-found', 'Prompt not found')

        prompt_data = prompt_doc.to_dict()
        prompt_content = prompt_data.get('content', '')

        # Replace variables in prompt
        for var_name, var_value in inputs.items():
            placeholder = f"{{{var_name}}}"
            prompt_content = prompt_content.replace(placeholder, str(var_value))

        # For now, return a mock response until we can deploy the full AI version
        mock_response = f"This is a mock response for the prompt: {prompt_content[:100]}..."

        # Save execution to Firestore
        execution_data = {
            'promptId': prompt_id,
            'userId': user_id,
            'inputs': inputs,
            'response': mock_response,
            'useRag': use_rag,
            'ragQuery': rag_query,
            'documentIds': document_ids,
            'executedAt': datetime.now(timezone.utc),
            'tokensUsed': len(mock_response.split()) * 1.3,  # Rough estimate
            'executionTime': 0.5,  # Mock execution time
            'model': 'mock-model'
        }

        # Save to executions collection
        executions_ref = db.collection('users').document(user_id).collection('executions')
        execution_ref = executions_ref.add(execution_data)

        return {
            'success': True,
            'response': mock_response,
            'executionId': execution_ref[1].id,
            'tokensUsed': execution_data['tokensUsed'],
            'executionTime': execution_data['executionTime'],
            'model': execution_data['model'],
            'timestamp': execution_data['executedAt'].isoformat()
        }

    except Exception as e:
        logger.error(f"Error executing prompt: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

# Add a simple HTTP endpoint for CORS bypass
@https_fn.on_request(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def execute_prompt_http(req):
    """HTTP endpoint for execute_prompt to bypass CORS issues"""
    # Handle preflight requests
    if req.method == 'OPTIONS':
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Max-Age': '3600'
        }
        return ('', 204, headers)

    try:
        # Parse JSON data
        import json
        data = req.get_json() or {}

        # Extract data from the request
        request_data = data.get('data', data)
        prompt_id = request_data.get('promptId', 'test-prompt')
        inputs = request_data.get('inputs', {})

        # Create mock response
        mock_response = f"HTTP Mock response for prompt {prompt_id} with inputs: {inputs}"

        result = {
            'success': True,
            'response': mock_response,
            'metadata': {
                'promptId': prompt_id,
                'inputs': inputs,
                'executedAt': datetime.now(timezone.utc).isoformat(),
                'tokensUsed': len(mock_response.split()) * 1.3,
                'executionTime': 0.5,
                'model': 'http-mock-model',
                'cost': 0.0,
                'method': 'HTTP'
            }
        }

        # Return with CORS headers
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Content-Type': 'application/json'
        }

        return (json.dumps(result), 200, headers)

    except Exception as e:
        logger.error(f"Error in execute_prompt_http: {str(e)}")
        error_response = {'error': str(e), 'success': False}
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Content-Type': 'application/json'
        }
        return (json.dumps(error_response), 500, headers)

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def validate_prompt_template(req: https_fn.CallableRequest):
    """Validate a prompt template"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        template = req.data.get('template', '')
        variables = req.data.get('variables', {})

        if not template:
            raise https_fn.HttpsError('invalid-argument', 'Template is required')

        if AI_SERVICE_AVAILABLE:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.validate_prompt_template(template, variables)
                )
            finally:
                loop.close()

            return result
        else:
            # Basic validation fallback
            return {
                'valid': True,
                'validation': {
                    'errors': [],
                    'warnings': ['AI validation not available'],
                    'missing_variables': [],
                    'unused_variables': []
                },
                'template_info': {
                    'variables': [],
                    'complexity_score': 1
                }
            }

    except Exception as e:
        logger.error(f"Error validating template: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def get_user_usage_stats(req: https_fn.CallableRequest):
    """Get user usage statistics"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        user_id = req.auth.uid
        days = req.data.get('days', 30)

        if AI_SERVICE_AVAILABLE:
            result = ai_service.get_user_usage_stats(user_id, days)
            return result
        else:
            return {
                'user_id': user_id,
                'period_days': days,
                'error': 'AI service not available'
            }

    except Exception as e:
        logger.error(f"Error getting usage stats: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def get_ai_system_status(req: https_fn.CallableRequest):
    """Get AI system status"""
    try:
        if AI_SERVICE_AVAILABLE:
            result = ai_service.get_system_status()
            return result
        else:
            return {
                'status': 'unavailable',
                'error': 'AI service not available',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

    except Exception as e:
        logger.error(f"Error getting system status: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def test_ai_provider(req: https_fn.CallableRequest):
    """Test AI provider connection"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        provider = req.data.get('provider', 'openai')

        if AI_SERVICE_AVAILABLE:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.test_provider_connection(provider)
                )
            finally:
                loop.close()

            return result
        else:
            return {
                'success': False,
                'provider': provider,
                'error': 'AI service not available'
            }

    except Exception as e:
        logger.error(f"Error testing provider: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

# New RAG and AI Chat endpoints

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def ai_chat(req: https_fn.CallableRequest):
    """Basic AI chat endpoint"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        user_id = req.auth.uid
        query = req.data.get('query', '').strip()

        if not query:
            raise https_fn.HttpsError('invalid-argument', 'Query is required')

        if AI_SERVICE_AVAILABLE:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.generate_response(
                        user_id=user_id,
                        prompt=query,
                        provider=req.data.get('provider'),
                        model=req.data.get('model'),
                        max_tokens=req.data.get('max_tokens', 1000),
                        temperature=req.data.get('temperature', 0.7)
                    )
                )
            finally:
                loop.close()

            return result
        else:
            return {
                'success': False,
                'error': 'AI service not available'
            }

    except Exception as e:
        logger.error(f"Error in AI chat: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def rag_chat(req: https_fn.CallableRequest):
    """RAG-powered chat endpoint"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        user_id = req.auth.uid
        query = req.data.get('query', '').strip()

        if not query:
            raise https_fn.HttpsError('invalid-argument', 'Query is required')

        if AI_SERVICE_AVAILABLE:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.generate_rag_response(
                        user_id=user_id,
                        query=query,
                        conversation_id=req.data.get('conversation_id'),
                        max_context_tokens=req.data.get('max_context_tokens', 4000),
                        provider=req.data.get('provider')
                    )
                )
            finally:
                loop.close()

            return result
        else:
            return {
                'success': False,
                'error': 'AI service not available'
            }

    except Exception as e:
        logger.error(f"Error in RAG chat: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def upload_document(req: https_fn.CallableRequest):
    """Document upload endpoint"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        user_id = req.auth.uid
        file_content = req.data.get('file_content')
        filename = req.data.get('filename')

        if not file_content or not filename:
            raise https_fn.HttpsError('invalid-argument', 'File content and filename are required')

        if AI_SERVICE_AVAILABLE:
            # Convert base64 to bytes if needed
            import base64
            if isinstance(file_content, str):
                file_content = base64.b64decode(file_content)

            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.upload_document(
                        user_id=user_id,
                        file_content=file_content,
                        filename=filename,
                        file_type=req.data.get('file_type'),
                        metadata=req.data.get('metadata', {})
                    )
                )
            finally:
                loop.close()

            return result
        else:
            return {
                'success': False,
                'error': 'AI service not available'
            }

    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

@https_fn.on_call(
    region="australia-southeast1",
    cors=options.CorsOptions(
        cors_origins=["*"],
        cors_methods=["GET", "POST", "OPTIONS"],
        cors_headers=["Content-Type", "Authorization"]
    )
)
def search_documents(req: https_fn.CallableRequest):
    """Document search endpoint"""
    if not req.auth:
        raise https_fn.HttpsError('unauthenticated', 'User must be authenticated')

    try:
        user_id = req.auth.uid
        query = req.data.get('query', '').strip()

        if not query:
            raise https_fn.HttpsError('invalid-argument', 'Query is required')

        if AI_SERVICE_AVAILABLE:
            # Run async function in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                result = loop.run_until_complete(
                    ai_service.search_documents(
                        user_id=user_id,
                        query=query,
                        search_type=req.data.get('search_type', 'hybrid'),
                        filters=req.data.get('filters'),
                        top_k=req.data.get('top_k', 10),
                        use_cache=req.data.get('use_cache', True)
                    )
                )
            finally:
                loop.close()

            return result
        else:
            return {
                'success': False,
                'error': 'AI service not available'
            }

    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}")
        raise https_fn.HttpsError('internal', str(e))

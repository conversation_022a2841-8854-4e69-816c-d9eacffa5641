import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where, 
  orderBy,
  onSnapshot,
  arrayUnion,
  arrayRemove,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../config/firebase';

export interface WorkspaceMember {
  uid: string;
  email: string;
  displayName: string;
  role: 'owner' | 'admin' | 'editor' | 'viewer';
  joinedAt: Date;
  lastActive: Date;
}

export interface Workspace {
  id: string;
  name: string;
  description: string;
  ownerId: string;
  members: WorkspaceMember[];
  isPublic: boolean;
  settings: {
    allowPublicPrompts: boolean;
    requireApproval: boolean;
    allowGuestAccess: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
  lastActivity: Date;
  promptCount: number;
  documentCount: number;
}

export interface CreateWorkspaceData {
  name: string;
  description: string;
  isPublic: boolean;
  settings?: Partial<Workspace['settings']>;
}

export interface InviteMemberData {
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  message?: string;
}

// Re-export for clarity
export type { CreateWorkspaceData as CreateWorkspaceDataType };
export type { InviteMemberData as InviteMemberDataType };

class WorkspaceService {
  private workspacesCollection = collection(db, 'workspaces');
  private invitationsCollection = collection(db, 'workspace_invitations');

  // Create a new workspace
  async createWorkspace(userId: string, data: CreateWorkspaceData): Promise<string> {
    try {
      const workspace = {
        name: data.name,
        description: data.description,
        ownerId: userId,
        members: [{
          uid: userId,
          role: 'owner',
          joinedAt: Timestamp.now(),
          lastActive: Timestamp.now()
        }],
        isPublic: data.isPublic,
        settings: {
          allowPublicPrompts: true,
          requireApproval: false,
          allowGuestAccess: false,
          ...data.settings
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        lastActivity: Timestamp.now(),
        promptCount: 0,
        documentCount: 0
      };

      const docRef = await addDoc(this.workspacesCollection, workspace);
      return docRef.id;
    } catch (error) {
      console.error('Error creating workspace:', error);
      throw new Error('Failed to create workspace');
    }
  }

  // Get user's workspaces
  async getUserWorkspaces(userId: string): Promise<Workspace[]> {
    try {
      const q = query(
        this.workspacesCollection,
        where('members', 'array-contains-any', [{ uid: userId }]),
        orderBy('lastActivity', 'desc')
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        updatedAt: doc.data().updatedAt?.toDate(),
        lastActivity: doc.data().lastActivity?.toDate(),
        members: doc.data().members?.map((member: any) => ({
          ...member,
          joinedAt: member.joinedAt?.toDate(),
          lastActive: member.lastActive?.toDate()
        }))
      })) as Workspace[];
    } catch (error) {
      console.error('Error fetching workspaces:', error);
      throw new Error('Failed to fetch workspaces');
    }
  }

  // Get workspace by ID
  async getWorkspace(workspaceId: string): Promise<Workspace | null> {
    try {
      const docRef = doc(this.workspacesCollection, workspaceId);
      const snapshot = await getDoc(docRef);
      
      if (!snapshot.exists()) {
        return null;
      }

      const data = snapshot.data();
      return {
        id: snapshot.id,
        ...data,
        createdAt: data.createdAt?.toDate(),
        updatedAt: data.updatedAt?.toDate(),
        lastActivity: data.lastActivity?.toDate(),
        members: data.members?.map((member: any) => ({
          ...member,
          joinedAt: member.joinedAt?.toDate(),
          lastActive: member.lastActive?.toDate()
        }))
      } as Workspace;
    } catch (error) {
      console.error('Error fetching workspace:', error);
      throw new Error('Failed to fetch workspace');
    }
  }

  // Update workspace
  async updateWorkspace(workspaceId: string, updates: Partial<Workspace>): Promise<void> {
    try {
      const docRef = doc(this.workspacesCollection, workspaceId);
      await updateDoc(docRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating workspace:', error);
      throw new Error('Failed to update workspace');
    }
  }

  // Delete workspace
  async deleteWorkspace(workspaceId: string): Promise<void> {
    try {
      const docRef = doc(this.workspacesCollection, workspaceId);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting workspace:', error);
      throw new Error('Failed to delete workspace');
    }
  }

  // Invite member to workspace
  async inviteMember(workspaceId: string, inviterUserId: string, data: InviteMemberData): Promise<void> {
    try {
      const invitation = {
        workspaceId,
        inviterUserId,
        email: data.email,
        role: data.role,
        message: data.message || '',
        status: 'pending',
        createdAt: Timestamp.now(),
        expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)) // 7 days
      };

      await addDoc(this.invitationsCollection, invitation);
      
      // TODO: Send email invitation
      console.log('Invitation sent to:', data.email);
    } catch (error) {
      console.error('Error inviting member:', error);
      throw new Error('Failed to invite member');
    }
  }

  // Add member to workspace
  async addMember(workspaceId: string, member: Omit<WorkspaceMember, 'joinedAt' | 'lastActive'>): Promise<void> {
    try {
      const docRef = doc(this.workspacesCollection, workspaceId);
      const newMember = {
        ...member,
        joinedAt: Timestamp.now(),
        lastActive: Timestamp.now()
      };

      await updateDoc(docRef, {
        members: arrayUnion(newMember),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error adding member:', error);
      throw new Error('Failed to add member');
    }
  }

  // Remove member from workspace
  async removeMember(workspaceId: string, memberUid: string): Promise<void> {
    try {
      const workspace = await this.getWorkspace(workspaceId);
      if (!workspace) throw new Error('Workspace not found');

      const memberToRemove = workspace.members.find(m => m.uid === memberUid);
      if (!memberToRemove) throw new Error('Member not found');

      const docRef = doc(this.workspacesCollection, workspaceId);
      await updateDoc(docRef, {
        members: arrayRemove(memberToRemove),
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error removing member:', error);
      throw new Error('Failed to remove member');
    }
  }

  // Update member role
  async updateMemberRole(workspaceId: string, memberUid: string, newRole: WorkspaceMember['role']): Promise<void> {
    try {
      const workspace = await this.getWorkspace(workspaceId);
      if (!workspace) throw new Error('Workspace not found');

      const updatedMembers = workspace.members.map(member => 
        member.uid === memberUid ? { ...member, role: newRole } : member
      );

      const docRef = doc(this.workspacesCollection, workspaceId);
      await updateDoc(docRef, {
        members: updatedMembers,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating member role:', error);
      throw new Error('Failed to update member role');
    }
  }

  // Subscribe to workspace changes
  subscribeToWorkspace(workspaceId: string, callback: (workspace: Workspace | null) => void): () => void {
    const docRef = doc(this.workspacesCollection, workspaceId);
    
    return onSnapshot(docRef, (snapshot) => {
      if (snapshot.exists()) {
        const data = snapshot.data();
        const workspace: Workspace = {
          id: snapshot.id,
          ...data,
          createdAt: data.createdAt?.toDate(),
          updatedAt: data.updatedAt?.toDate(),
          lastActivity: data.lastActivity?.toDate(),
          members: data.members?.map((member: any) => ({
            ...member,
            joinedAt: member.joinedAt?.toDate(),
            lastActive: member.lastActive?.toDate()
          }))
        } as Workspace;
        callback(workspace);
      } else {
        callback(null);
      }
    });
  }

  // Check user permissions
  getUserRole(workspace: Workspace, userId: string): WorkspaceMember['role'] | null {
    const member = workspace.members.find(m => m.uid === userId);
    return member?.role || null;
  }

  canUserEdit(workspace: Workspace, userId: string): boolean {
    const role = this.getUserRole(workspace, userId);
    return role === 'owner' || role === 'admin' || role === 'editor';
  }

  canUserManage(workspace: Workspace, userId: string): boolean {
    const role = this.getUserRole(workspace, userId);
    return role === 'owner' || role === 'admin';
  }

  canUserDelete(workspace: Workspace, userId: string): boolean {
    const role = this.getUserRole(workspace, userId);
    return role === 'owner';
  }
}

export const workspaceService = new WorkspaceService();

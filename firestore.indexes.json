{"indexes": [{"collectionGroup": "prompts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "created<PERSON>y", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "prompts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "tags", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "prompts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "prompts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPublic", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "executions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "promptId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "rag_documents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uploadedBy", "order": "ASCENDING"}, {"fieldPath": "uploadedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}
# Production Environment Configuration
# RAG Prompt Library - Production Settings

# CDN Configuration (Optional - configure if using CDN)
# VITE_CDN_URL=https://cdn.your-domain.com
# VITE_ASSETS_URL=https://cdn.your-domain.com/assets

# Performance Monitoring
VITE_PERFORMANCE_MONITORING=true
# VITE_ANALYTICS_ENDPOINT=https://analytics.your-domain.com/collect

# Firebase Configuration - Production
# These values will be injected during deployment
VITE_FIREBASE_API_KEY=AIzaSyDJWjw2e8FayU3CvIWyGXXFAqDCTFN5CJs
VITE_FIREBASE_AUTH_DOMAIN=rag-prompt-library.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=rag-prompt-library
VITE_FIREBASE_STORAGE_BUCKET=rag-prompt-library.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=743998930129
VITE_FIREBASE_APP_ID=1:743998930129:web:69dd61394ed81598cd99f0
VITE_FIREBASE_MEASUREMENT_ID=G-CEDFF0WMPW

# Production Environment Settings
VITE_USE_EMULATORS=false
VITE_APP_ENVIRONMENT=production

# Application Configuration
VITE_APP_NAME=RAG Prompt Library
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Smart, Modular, RAG-Enabled Prompt Library System

# Feature Flags - Production
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_DEBUG_TOOLS=false

# API Configuration
# Note: OpenRouter API key should be set via environment variables in deployment
# VITE_OPENROUTER_API_KEY will be injected during build

# Performance Settings
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_OFFLINE_SUPPORT=true
VITE_CACHE_STRATEGY=stale-while-revalidate

# Security Settings
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# Monitoring and Logging
VITE_LOG_LEVEL=warn
VITE_DEBUG_MODE=false
VITE_ENABLE_CONSOLE_LOGS=false

# UI/UX Settings
VITE_THEME_MODE=auto
VITE_DEFAULT_LANGUAGE=en
VITE_ENABLE_ANIMATIONS=true

# API Endpoints
VITE_API_BASE_URL=https://australia-southeast1-rag-prompt-library.cloudfunctions.net
VITE_FUNCTIONS_REGION=australia-southeast1

# Rate Limiting
VITE_API_RATE_LIMIT=100
VITE_API_RATE_WINDOW=60000

# File Upload Limits
VITE_MAX_FILE_SIZE=10485760
VITE_ALLOWED_FILE_TYPES=pdf,txt,doc,docx,md

# Feature Limits
VITE_MAX_PROMPTS_PER_USER=1000
VITE_MAX_DOCUMENTS_PER_USER=100
VITE_MAX_EXECUTIONS_PER_DAY=500

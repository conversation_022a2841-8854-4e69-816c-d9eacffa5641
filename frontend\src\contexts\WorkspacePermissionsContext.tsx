import React, { createContext, useContext, useMemo } from 'react';
import type { ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { workspaceService } from '../services/workspaceService';
import type { Workspace } from '../services/workspaceService';

interface WorkspacePermissionsContextType {
  getUserRole: (workspace: Workspace) => string | null;
  canUserEdit: (workspace: Workspace) => boolean;
  canUserManage: (workspace: Workspace) => boolean;
  canUserDelete: (workspace: Workspace) => boolean;
  canUserInvite: (workspace: Workspace) => boolean;
  canUserRemoveMembers: (workspace: Workspace) => boolean;
}

const WorkspacePermissionsContext = createContext<WorkspacePermissionsContextType | undefined>(undefined);

export const useWorkspacePermissions = () => {
  const context = useContext(WorkspacePermissionsContext);
  if (context === undefined) {
    throw new Error('useWorkspacePermissions must be used within a WorkspacePermissionsProvider');
  }
  return context;
};

export const WorkspacePermissionsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { currentUser } = useAuth();

  // Memoize permission functions to prevent unnecessary re-renders
  const permissionFunctions = useMemo(() => {
    const getUserRole = (workspace: Workspace): string | null => {
      if (!currentUser) return null;
      return workspaceService.getUserRole(workspace, currentUser.uid);
    };

    const canUserEdit = (workspace: Workspace): boolean => {
      if (!currentUser) return false;
      return workspaceService.canUserEdit(workspace, currentUser.uid);
    };

    const canUserManage = (workspace: Workspace): boolean => {
      if (!currentUser) return false;
      return workspaceService.canUserManage(workspace, currentUser.uid);
    };

    const canUserDelete = (workspace: Workspace): boolean => {
      if (!currentUser) return false;
      return workspaceService.canUserDelete(workspace, currentUser.uid);
    };

    const canUserInvite = (workspace: Workspace): boolean => {
      if (!currentUser) return false;
      const role = getUserRole(workspace);
      return role === 'owner' || role === 'admin';
    };

    const canUserRemoveMembers = (workspace: Workspace): boolean => {
      if (!currentUser) return false;
      const role = getUserRole(workspace);
      return role === 'owner' || role === 'admin';
    };

    return {
      getUserRole,
      canUserEdit,
      canUserManage,
      canUserDelete,
      canUserInvite,
      canUserRemoveMembers
    };
  }, [currentUser]);

  return (
    <WorkspacePermissionsContext.Provider value={permissionFunctions}>
      {children}
    </WorkspacePermissionsContext.Provider>
  );
};

#!/usr/bin/env python3
"""
Test core RAG logic without Firebase dependencies
"""
import asyncio
import os
import numpy as np

# Set up environment for testing
os.environ['OPENROUTER_API_KEY'] = 'test-key'
os.environ['OPENROUTER_API_KEY'] = 'test-key'

from src.rag.text_chunker import TextChunker, ChunkingConfig
from src.rag.embedding_generator import EmbeddingConfig
from src.llm.openrouter_client import OpenRouterConfig
from src.llm.token_counter import TokenCounter

async def test_text_chunking():
    """Test text chunking functionality"""
    print("🧪 Testing Text Chunking...")
    
    try:
        config = ChunkingConfig(
            chunk_size=100,
            chunk_overlap=20,
            separators=["\n\n", "\n", ". ", " "]
        )
        chunker = TextChunker(config)
        
        test_text = """
        This is a comprehensive test document for the RAG system.
        
        The document contains multiple paragraphs to test the chunking functionality.
        Each paragraph should be processed and split appropriately.
        
        The RAG system should be able to extract this text, chunk it properly,
        and prepare it for embedding generation and vector storage.
        
        This test verifies that all components work together correctly.
        The chunking algorithm should respect sentence boundaries when possible.
        """
        
        chunks = chunker.chunk_text(test_text.strip(), 'test-doc', {
            'source': 'test_document.txt'
        })
        
        print(f"✅ Created {len(chunks)} chunks from {len(test_text)} characters")
        
        for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
            print(f"   Chunk {i+1}: {len(chunk['content'])} chars - {chunk['content'][:50]}...")
        
        # Verify chunk properties
        assert len(chunks) > 0, "Should create at least one chunk"
        assert all('id' in chunk for chunk in chunks), "All chunks should have IDs"
        assert all('content' in chunk for chunk in chunks), "All chunks should have content"
        assert all('metadata' in chunk for chunk in chunks), "All chunks should have metadata"
        
        print("✅ All chunk validation tests passed")
        return True
        
    except Exception as e:
        print(f"❌ Text chunking test failed: {e}")
        return False

def test_configurations():
    """Test configuration classes"""
    print("\n🧪 Testing Configuration Classes...")
    
    try:
        # Test ChunkingConfig
        chunk_config = ChunkingConfig(
            chunk_size=500,
            chunk_overlap=100,
            separators=["\n\n", "\n", ". "]
        )
        print(f"✅ ChunkingConfig: size={chunk_config.chunk_size}, overlap={chunk_config.chunk_overlap}")
        
        # Test EmbeddingConfig
        embed_config = EmbeddingConfig(
            model="text-embedding-3-small",
            dimensions=1536,
            batch_size=10
        )
        print(f"✅ EmbeddingConfig: model={embed_config.model}, dims={embed_config.dimensions}")
        
        # Test OpenRouterConfig
        llm_config = OpenRouterConfig(
            api_key="test-key",
            model="anthropic/claude-3.5-sonnet",
            max_tokens=2000,
            temperature=0.7
        )
        print(f"✅ OpenRouterConfig: model={llm_config.model}, max_tokens={llm_config.max_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_token_counter():
    """Test token counting functionality"""
    print("\n🧪 Testing Token Counter...")
    
    try:
        counter = TokenCounter()
        
        test_text = "This is a test sentence for token counting."
        token_count = counter.count_tokens(test_text)
        
        print(f"✅ Token count for '{test_text}': {token_count} tokens")
        
        # Test with longer text
        long_text = "This is a much longer text. " * 50
        long_count = counter.count_tokens(long_text)
        print(f"✅ Token count for long text ({len(long_text)} chars): {long_count} tokens")
        
        # Verify reasonable token counts
        assert token_count > 0, "Should count some tokens"
        assert long_count > token_count, "Longer text should have more tokens"
        
        return True
        
    except Exception as e:
        print(f"❌ Token counter test failed: {e}")
        return False

def test_vector_operations():
    """Test basic vector operations for FAISS"""
    print("\n🧪 Testing Vector Operations...")
    
    try:
        import faiss
        
        # Test FAISS installation and basic operations
        dimension = 128
        index = faiss.IndexFlatIP(dimension)
        
        # Create some test vectors
        test_vectors = np.random.random((5, dimension)).astype('float32')
        faiss.normalize_L2(test_vectors)
        
        # Add vectors to index
        index.add(test_vectors)
        
        print(f"✅ FAISS index created with {index.ntotal} vectors")
        
        # Test search
        query_vector = np.random.random((1, dimension)).astype('float32')
        faiss.normalize_L2(query_vector)
        
        scores, indices = index.search(query_vector, 3)
        print(f"✅ Search returned {len(indices[0])} results with scores: {scores[0][:3]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vector operations test failed: {e}")
        return False

async def test_integration_flow():
    """Test the integration flow without external dependencies"""
    print("\n🔗 Testing Integration Flow...")
    
    try:
        # Step 1: Text chunking
        config = ChunkingConfig(chunk_size=200, chunk_overlap=50)
        chunker = TextChunker(config)
        
        sample_text = """
        Artificial Intelligence (AI) is transforming how we work and live.
        
        Machine learning algorithms can process vast amounts of data to identify patterns
        and make predictions. This capability is being applied across industries from
        healthcare to finance to transportation.
        
        Natural Language Processing (NLP) allows computers to understand and generate
        human language. This enables applications like chatbots, translation services,
        and document analysis.
        
        The future of AI holds great promise for solving complex global challenges
        while also raising important questions about ethics and responsible development.
        """
        
        chunks = chunker.chunk_text(sample_text.strip(), 'ai-overview', {
            'source': 'ai_document.txt'
        })
        
        print(f"✅ Step 1: Created {len(chunks)} chunks")
        
        # Step 2: Simulate embedding generation (mock)
        mock_embeddings = []
        for chunk in chunks:
            # Create mock embedding (normally would use OpenAI API)
            mock_embedding = np.random.random(384).tolist()  # Simulate embedding
            chunk['embedding'] = mock_embedding
            mock_embeddings.append(chunk)
        
        print(f"✅ Step 2: Generated {len(mock_embeddings)} mock embeddings")
        
        # Step 3: Simulate vector storage (mock)
        print("✅ Step 3: Would store vectors in FAISS index")
        
        # Step 4: Simulate retrieval (mock)
        query = "What is machine learning?"
        print(f"✅ Step 4: Would retrieve relevant chunks for query: '{query}'")
        
        print("🎉 Integration flow test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Integration flow test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 RAG Core Logic Test Suite")
    print("=" * 60)
    
    tests = [
        test_configurations(),
        test_token_counter(),
        test_vector_operations(),
        await test_text_chunking(),
        await test_integration_flow()
    ]
    
    passed = sum(tests)
    total = len(tests)
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! RAG pipeline core logic is working correctly.")
        print("\n📋 Next Steps:")
        print("1. ✅ Core RAG components are functional")
        print("2. ⚠️  Need valid API keys for OpenAI embeddings")
        print("3. ⚠️  Need Firebase credentials for cloud deployment")
        print("4. 🚀 Ready for production deployment with proper configuration")
    else:
        print(f"⚠️  {total - passed} tests failed. Please review the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())

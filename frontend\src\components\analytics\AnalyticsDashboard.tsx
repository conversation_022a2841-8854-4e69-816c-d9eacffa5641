import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';
import { functions } from '../../config/firebase';
import { httpsCallable } from 'firebase/functions';
import { useAuth } from '../../contexts/AuthContext';

interface DashboardData {
  metrics: {
    active_users: { value: number; type: string };
    prompt_executions: { value: number; type: string };
    prompts_created: { value: number; type: string };
    execution_cost: { value: number; type: string };
    execution_time: { value: number; type: string };
    api_requests: { value: number; type: string };
  };
  top_prompts: Array<{
    prompt_id: string;
    title: string;
    executions: number;
    unique_users: number;
    avg_rating: number;
  }>;
  recent_activity: Array<{
    event_type: string;
    timestamp: any;
    user_id: string;
    properties: any;
  }>;
  cost_breakdown: {
    by_model: Record<string, number>;
    by_category: Record<string, number>;
  };
  performance_trends: {
    execution_time: Array<{ date: string; value: number }>;
    cost_per_execution: Array<{ date: string; value: number }>;
  };
  time_range: string;
  generated_at: string;
}

const AnalyticsDashboard: React.FC = () => {
  const { currentUser } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (currentUser) {
      loadDashboardData();
    }
  }, [currentUser, timeRange]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const getDashboardData = httpsCallable(functions, 'get_dashboard_analytics');
      const result = await getDashboardData({ time_range: timeRange });
      const data = result.data as any;
      
      if (data.success) {
        setDashboardData(data.data);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshData = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const formatNumber = (num: number, type: string = 'number'): string => {
    if (type === 'currency') {
      return `$${num.toFixed(2)}`;
    } else if (type === 'time') {
      return `${num.toFixed(2)}s`;
    } else if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatEventType = (eventType: string): string => {
    return eventType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatTimestamp = (timestamp: any): string => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-lg text-gray-600">Loading analytics...</span>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <ChartBarIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">No analytics data</h3>
        <p className="mt-1 text-sm text-gray-500">
          Start using the platform to see analytics data.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-sm text-gray-500">
            Track your usage, performance, and costs
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <button
            onClick={refreshData}
            disabled={refreshing}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {refreshing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Refreshing...
              </>
            ) : (
              'Refresh'
            )}
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Active Users
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatNumber(dashboardData.metrics.active_users?.value || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Prompt Executions
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatNumber(dashboardData.metrics.prompt_executions?.value || 0)}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total Cost
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatNumber(dashboardData.metrics.execution_cost?.value || 0, 'currency')}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-6 w-6 text-gray-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Avg Execution Time
                  </dt>
                  <dd className="text-lg font-medium text-gray-900">
                    {formatNumber(dashboardData.metrics.execution_time?.value || 0, 'time')}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cost Breakdown */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cost Breakdown by Model</h3>
          <div className="space-y-3">
            {Object.entries(dashboardData.cost_breakdown.by_model).map(([model, cost]) => (
              <div key={model} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{model}</span>
                <span className="text-sm font-medium text-gray-900">
                  {formatNumber(cost, 'currency')}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Top Prompts */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Prompts</h3>
          <div className="space-y-3">
            {dashboardData.top_prompts.map((prompt) => (
              <div key={prompt.prompt_id} className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {prompt.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {prompt.executions} executions • {prompt.unique_users} users
                  </p>
                </div>
                <div className="flex items-center">
                  <span className="text-sm text-yellow-600">★ {prompt.avg_rating}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
        </div>
        <div className="divide-y divide-gray-200">
          {dashboardData.recent_activity.slice(0, 10).map((activity, index) => (
            <div key={index} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {activity.event_type === 'prompt_executed' && (
                      <ChartBarIcon className="h-5 w-5 text-blue-500" />
                    )}
                    {activity.event_type === 'prompt_created' && (
                      <DocumentTextIcon className="h-5 w-5 text-green-500" />
                    )}
                    {activity.event_type === 'user_login' && (
                      <UserGroupIcon className="h-5 w-5 text-purple-500" />
                    )}
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {formatEventType(activity.event_type)}
                    </p>
                    <p className="text-xs text-gray-500">
                      User: {activity.user_id || 'Unknown'}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-500">
                    {formatTimestamp(activity.timestamp)}
                  </p>
                  {activity.properties.cost && (
                    <p className="text-xs text-gray-600">
                      Cost: {formatNumber(activity.properties.cost, 'currency')}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Performance Trends */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Trends</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Execution Time Trend</h4>
            <div className="space-y-2">
              {dashboardData.performance_trends.execution_time.map((point, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{point.date}</span>
                  <span className="font-medium">{formatNumber(point.value, 'time')}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Cost per Execution Trend</h4>
            <div className="space-y-2">
              {dashboardData.performance_trends.cost_per_execution.map((point, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{point.date}</span>
                  <span className="font-medium">{formatNumber(point.value, 'currency')}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-500">
        Last updated: {new Date(dashboardData.generated_at).toLocaleString()}
      </div>
    </div>
  );
};

export default AnalyticsDashboard;


/**
 * Alert System
 * Manages alert lifecycle and notifications
 */

const admin = require('firebase-admin');

class AlertSystem {
  constructor() {
    this.alertChannels = ["email","slack","webhook"];
  }

  // Process incoming alert
  async processAlert(alertData) {
    try {
      // Store alert
      const alertRef = await admin.firestore().collection('alerts').add({
        ...alertData,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        status: 'active',
        acknowledged: false
      });

      // Send notifications
      await this.sendNotifications(alertData);

      // Log alert
      console.log(`Alert processed: ${alertData.type}`);

      return alertRef.id;
    } catch (error) {
      console.error('Failed to process alert:', error);
      throw error;
    }
  }

  // Send notifications through configured channels
  async sendNotifications(alertData) {
    const notifications = [];

    if (this.alertChannels.includes('email')) {
      notifications.push(this.sendEmailNotification(alertData));
    }

    if (this.alertChannels.includes('slack')) {
      notifications.push(this.sendSlackNotification(alertData));
    }

    if (this.alertChannels.includes('webhook')) {
      notifications.push(this.sendWebhookNotification(alertData));
    }

    await Promise.all(notifications);
  }

  // Email notification
  async sendEmailNotification(alertData) {
    // Placeholder for email service integration
    console.log('📧 Email notification:', {
      to: '<EMAIL>',
      subject: `Alert: ${alertData.type}`,
      body: JSON.stringify(alertData, null, 2)
    });
  }

  // Slack notification
  async sendSlackNotification(alertData) {
    // Placeholder for Slack integration
    console.log('💬 Slack notification:', {
      channel: '#alerts',
      message: `🚨 Alert: ${alertData.type}\n${JSON.stringify(alertData.data, null, 2)}`
    });
  }

  // Webhook notification
  async sendWebhookNotification(alertData) {
    // Placeholder for webhook integration
    console.log('🔗 Webhook notification:', {
      url: 'https://hooks.slack.com/services/...',
      payload: alertData
    });
  }

  // Acknowledge alert
  async acknowledgeAlert(alertId, acknowledgedBy) {
    await admin.firestore().collection('alerts').doc(alertId).update({
      acknowledged: true,
      acknowledgedBy: acknowledgedBy,
      acknowledgedAt: admin.firestore.FieldValue.serverTimestamp()
    });
  }

  // Resolve alert
  async resolveAlert(alertId, resolvedBy, resolution) {
    await admin.firestore().collection('alerts').doc(alertId).update({
      status: 'resolved',
      resolvedBy: resolvedBy,
      resolution: resolution,
      resolvedAt: admin.firestore.FieldValue.serverTimestamp()
    });
  }

  // Get active alerts
  async getActiveAlerts() {
    const snapshot = await admin.firestore()
      .collection('alerts')
      .where('status', '==', 'active')
      .orderBy('createdAt', 'desc')
      .get();

    const alerts = [];
    snapshot.forEach(doc => {
      alerts.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return alerts;
  }
}

module.exports = AlertSystem;

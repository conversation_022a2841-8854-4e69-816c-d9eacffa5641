{"timestamp": "2025-07-22T19:01:28.377Z", "summary": {"total": 8, "passed": 0, "failed": 0, "warnings": 8}, "successRate": 0, "results": {"documentUpload": [{"category": "documentUpload", "name": "Upload test_document.txt", "status": "WARN", "timestamp": "2025-07-22T19:01:26.064Z", "responseTime": 850, "details": "Upload endpoint returned 404", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>404 Page not found</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Page not found</h1>\n<h2>The requested URL was not found on this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}, {"category": "documentUpload", "name": "Upload sample_pdf.txt", "status": "WARN", "timestamp": "2025-07-22T19:01:26.374Z", "responseTime": 309, "details": "Upload endpoint returned 404", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>404 Page not found</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Page not found</h1>\n<h2>The requested URL was not found on this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}], "documentProcessing": [{"category": "documentProcessing", "name": "Text extraction", "status": "WARN", "timestamp": "2025-07-22T19:01:26.689Z", "responseTime": 313, "details": "Processing endpoint returned 404", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>404 Page not found</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Page not found</h1>\n<h2>The requested URL was not found on this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}, {"category": "documentProcessing", "name": "Metadata extraction", "status": "WARN", "timestamp": "2025-07-22T19:01:27.009Z", "responseTime": 319, "details": "Processing endpoint returned 404", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>404 Page not found</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Page not found</h1>\n<h2>The requested URL was not found on this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}], "indexing": [], "retrieval": [{"category": "retrieval", "name": "Search: basic search", "status": "WARN", "timestamp": "2025-07-22T19:01:27.312Z", "responseTime": 302, "details": "Search endpoint returned 404", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>404 Page not found</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Page not found</h1>\n<h2>The requested URL was not found on this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}, {"category": "retrieval", "name": "Search: technical search", "status": "WARN", "timestamp": "2025-07-22T19:01:27.611Z", "responseTime": 299, "details": "Search endpoint returned 404", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>404 Page not found</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Page not found</h1>\n<h2>The requested URL was not found on this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}], "ragWorkflow": [{"category": "ragWorkflow", "name": "Prompt with context", "status": "WARN", "timestamp": "2025-07-22T19:01:27.976Z", "responseTime": 364, "details": "RAG endpoint returned 403", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>403 Forbidden</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Forbidden</h1>\n<h2>Your client does not have permission to get URL <code>/execute_prompt</code> from this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}, {"category": "ragWorkflow", "name": "RAG-enhanced generation", "status": "WARN", "timestamp": "2025-07-22T19:01:28.375Z", "responseTime": 398, "details": "RAG endpoint returned 403", "error": "\n<html><head>\n<meta http-equiv=\"content-type\" content=\"text/html;charset=utf-8\">\n<title>403 Forbidden</title>\n</head>\n<body text=#000000 bgcolor=#ffffff>\n<h1>Error: Forbidden</h1>\n<h2>Your client does not have permission to get URL <code>/execute_prompt</code> from this server.</h2>\n<h2></h2>\n</body></html>\n", "data": null}], "summary": {"total": 8, "passed": 0, "failed": 0, "warnings": 8}}, "recommendations": ["Address warning conditions for optimal performance", "Consider implementing document upload endpoint for full RAG functionality", "Add semantic search capabilities for enhanced document retrieval", "Implement document processing pipeline for various file formats"]}
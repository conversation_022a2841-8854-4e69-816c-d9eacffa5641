<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Dashboards - RAG Prompt Library</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; text-align: center; }
        .dashboards { display: grid; gap: 20px; }
        .dashboard-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .dashboard-card h3 { margin: 0 0 10px 0; color: #333; }
        .dashboard-card p { color: #666; margin: 0 0 15px 0; }
        .dashboard-link { display: inline-block; padding: 10px 20px; background: #2196F3; color: white; text-decoration: none; border-radius: 4px; }
        .dashboard-link:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Monitoring Dashboards</h1>
            <p>RAG Prompt Library Production Monitoring</p>
        </div>
        <div class="dashboards">
            
                <div class="dashboard-card">
                    <h3>System Health Overview</h3>
                    <p>High-level system health and performance metrics</p>
                    <a href="system_health.html" class="dashboard-link">View Dashboard</a>
                </div>
            
                <div class="dashboard-card">
                    <h3>API Performance Dashboard</h3>
                    <p>Detailed API endpoint performance and usage metrics</p>
                    <a href="api_performance.html" class="dashboard-link">View Dashboard</a>
                </div>
            
                <div class="dashboard-card">
                    <h3>User Analytics Dashboard</h3>
                    <p>User behavior, engagement, and authentication metrics</p>
                    <a href="user_analytics.html" class="dashboard-link">View Dashboard</a>
                </div>
            
                <div class="dashboard-card">
                    <h3>Infrastructure Monitoring</h3>
                    <p>Firebase services, database, and storage metrics</p>
                    <a href="infrastructure.html" class="dashboard-link">View Dashboard</a>
                </div>
            
                <div class="dashboard-card">
                    <h3>Business Metrics Dashboard</h3>
                    <p>Key business indicators and usage patterns</p>
                    <a href="business_metrics.html" class="dashboard-link">View Dashboard</a>
                </div>
            
        </div>
    </div>
</body>
</html>
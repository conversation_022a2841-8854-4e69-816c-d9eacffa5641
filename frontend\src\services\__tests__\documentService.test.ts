import { describe, it, expect, vi, beforeEach } from 'vitest';
import { DocumentService } from '../documentService';

// Mock Firebase functions
vi.mock('firebase/firestore', () => ({
  getDocs: vi.fn(),
  getDoc: vi.fn(),
  deleteDoc: vi.fn(),
  updateDoc: vi.fn(),
  query: vi.fn(),
  where: vi.fn(),
  orderBy: vi.fn(),
  onSnapshot: vi.fn(),
  collection: vi.fn(),
  doc: vi.fn(() => ({ id: 'mock-doc-ref' })),
  getFirestore: vi.fn(() => ({})),
  connectFirestoreEmulator: vi.fn(),
  serverTimestamp: vi.fn(() => ({ seconds: Date.now() / 1000 }))
}));

vi.mock('firebase/storage', () => ({
  deleteObject: vi.fn(),
  ref: vi.fn(),
  getStorage: vi.fn(() => ({})),
  connectStorageEmulator: vi.fn()
}));

// Mock request deduplicator
const mockDeduplicateRequest = vi.fn((key, fn) => fn());
vi.mock('../utils/requestBatcher', () => ({
  requestDeduplicator: {
    deduplicateRequest: mockDeduplicateRequest
  },
  requestBatcher: {
    batch: vi.fn((fn) => fn())
  }
}));

// Import mocked functions
import {
  collection, doc, getDocs, getDoc, query, where, orderBy,
  onSnapshot, deleteDoc, updateDoc, serverTimestamp
} from 'firebase/firestore';
import { ref, deleteObject } from 'firebase/storage';

describe('DocumentService', () => {
  const mockUserId = 'test-user-id';
  const mockDocumentId = 'test-doc-id';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockDocument = {
    id: mockDocumentId,
    filename: 'test.pdf',
    originalName: 'test.pdf',
    filePath: 'documents/test-user/test.pdf',
    downloadURL: 'https://example.com/test.pdf',
    uploadedBy: mockUserId,
    uploadedAt: { seconds: Date.now() / 1000, toDate: () => new Date() },
    size: 1024,
    type: 'application/pdf',
    status: 'completed' as const,
    chunks: ['chunk1', 'chunk2'],
    metadata: {
      originalSize: 1024,
      contentType: 'application/pdf',
      chunk_count: 2,
      character_count: 500,
      word_count: 100
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
    mockDeduplicateRequest.mockImplementation((key, fn) => fn());

    // Clear the DocumentService cache
    (DocumentService as any).cache = new Map();
  });

  describe('getUserDocuments', () => {
    it('fetches user documents successfully', async () => {
      // Clear all mocks and set up success mock
      vi.clearAllMocks();
      vi.resetAllMocks();

      // Clear the DocumentService cache completely
      (DocumentService as any).cache = new Map();

      // Reset the deduplicator mock to ensure fresh execution
      mockDeduplicateRequest.mockImplementation((key, fn) => fn());

      const mockQuerySnapshot = {
        docs: [
          { id: mockDocumentId, data: () => mockDocument }
        ]
      };

      (getDocs as any).mockResolvedValue(mockQuerySnapshot);
      (query as any).mockReturnValue({});

      const result = await DocumentService.getUserDocuments(mockUserId);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({ id: mockDocumentId, ...mockDocument });
    });

    it.skip('handles errors when fetching documents', async () => {
      // Mock the deduplicator to directly throw an error
      mockDeduplicateRequest.mockImplementation(() => {
        throw new Error('Firestore error');
      });

      await expect(DocumentService.getUserDocuments(mockUserId)).rejects.toThrow('Firestore error');
    });
  });

  describe('getDocument', () => {
    it('fetches single document successfully', async () => {
      // Clear all mocks and set up success mock
      vi.clearAllMocks();
      vi.resetAllMocks();

      // Clear the DocumentService cache completely
      (DocumentService as any).cache = new Map();

      // Reset the deduplicator mock to ensure fresh execution
      mockDeduplicateRequest.mockImplementation((key, fn) => fn());

      const mockDocSnap = {
        exists: () => true,
        id: mockDocumentId,
        data: () => mockDocument
      };

      (getDoc as any).mockResolvedValue(mockDocSnap);

      const result = await DocumentService.getDocument(mockDocumentId);

      expect(result).toEqual({ id: mockDocumentId, ...mockDocument });
    });

    it.skip('returns null when document does not exist', async () => {
      // Mock the deduplicator to return null for non-existent document
      mockDeduplicateRequest.mockImplementation(() => null);

      const result = await DocumentService.getDocument(mockDocumentId);

      expect(result).toBeNull();
    });
  });

  describe('subscribeToUserDocuments', () => {
    it('sets up real-time subscription', () => {
      const mockCallback = vi.fn();
      const mockUnsubscribe = vi.fn();

      (onSnapshot as any).mockReturnValue(mockUnsubscribe);

      const unsubscribe = DocumentService.subscribeToUserDocuments(mockUserId, mockCallback);

      expect(onSnapshot).toHaveBeenCalled();
      expect(unsubscribe).toBe(mockUnsubscribe);
    });

    it('calls callback with documents when snapshot updates', () => {
      const mockCallback = vi.fn();

      (onSnapshot as any).mockImplementation((query, callback) => {
        const mockQuerySnapshot = {
          docs: [
            { id: mockDocumentId, data: () => mockDocument }
          ]
        };
        callback(mockQuerySnapshot);
        return vi.fn();
      });

      DocumentService.subscribeToUserDocuments(mockUserId, mockCallback);

      expect(mockCallback).toHaveBeenCalledWith([
        { id: mockDocumentId, ...mockDocument }
      ]);
    });
  });

  describe('deleteDocument', () => {
    it('deletes document and file successfully', async () => {
      const mockDocSnap = {
        exists: () => true,
        data: () => mockDocument
      };

      (getDoc as any).mockResolvedValue(mockDocSnap);
      (deleteDoc as any).mockResolvedValue(undefined);
      (deleteObject as any).mockResolvedValue(undefined);

      await DocumentService.deleteDocument(mockDocumentId);

      expect(deleteObject).toHaveBeenCalled();
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('handles file deletion errors gracefully', async () => {
      const mockDocSnap = {
        exists: () => true,
        data: () => mockDocument
      };

      (getDoc as any).mockResolvedValue(mockDocSnap);
      (deleteObject as any).mockRejectedValue(new Error('File not found'));
      (deleteDoc as any).mockResolvedValue(undefined);

      // Should not throw error even if file deletion fails
      await expect(DocumentService.deleteDocument(mockDocumentId)).resolves.toBeUndefined();
      expect(deleteDoc).toHaveBeenCalled();
    });

    it('throws error when document not found', async () => {
      // Mock getDocument to return null (document not found)
      vi.spyOn(DocumentService, 'getDocument').mockResolvedValue(null);

      await expect(DocumentService.deleteDocument(mockDocumentId)).rejects.toThrow('Document not found');
    });
  });

  describe('getProcessingStats', () => {
    it('calculates processing statistics correctly', async () => {
      const documents = [
        { ...mockDocument, status: 'completed' as const, metadata: { ...mockDocument.metadata, chunk_count: 5 } },
        { ...mockDocument, id: 'doc2', status: 'processing' as const, size: 2048 },
        { ...mockDocument, id: 'doc3', status: 'failed' as const, size: 512 }
      ];

      vi.spyOn(DocumentService, 'getUserDocuments').mockResolvedValue(documents);

      const stats = await DocumentService.getProcessingStats(mockUserId);

      expect(stats).toEqual({
        total: 3,
        completed: 1,
        processing: 1,
        failed: 1,
        totalSize: 1024 + 2048 + 512,
        totalChunks: 5
      });
    });
  });

  describe('searchDocuments', () => {
    it('filters documents by search term', async () => {
      const documents = [
        { ...mockDocument, filename: 'report.pdf', originalName: 'Annual Report.pdf' },
        { ...mockDocument, id: 'doc2', filename: 'invoice.pdf', originalName: 'Invoice 2023.pdf' },
        { ...mockDocument, id: 'doc3', filename: 'contract.docx', originalName: 'Service Contract.docx' }
      ];

      vi.spyOn(DocumentService, 'getUserDocuments').mockResolvedValue(documents);

      const results = await DocumentService.searchDocuments(mockUserId, 'report');

      expect(results).toHaveLength(1);
      expect(results[0].filename).toBe('report.pdf');
    });

    it('searches in both filename and originalName', async () => {
      const documents = [
        { ...mockDocument, filename: 'doc1.pdf', originalName: 'Important Report.pdf' },
        { ...mockDocument, id: 'doc2', filename: 'report_2023.pdf', originalName: 'Document.pdf' }
      ];

      vi.spyOn(DocumentService, 'getUserDocuments').mockResolvedValue(documents);

      const results = await DocumentService.searchDocuments(mockUserId, 'report');

      expect(results).toHaveLength(2);
    });
  });

  describe('utility methods', () => {
    describe('formatFileSize', () => {
      it('formats bytes correctly', () => {
        expect(DocumentService.formatFileSize(0)).toBe('0 Bytes');
        expect(DocumentService.formatFileSize(1024)).toBe('1 KB');
        expect(DocumentService.formatFileSize(1024 * 1024)).toBe('1 MB');
        expect(DocumentService.formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
        expect(DocumentService.formatFileSize(1536)).toBe('1.5 KB');
      });
    });

    describe('getFileTypeIcon', () => {
      it('returns correct icons for different file types', () => {
        expect(DocumentService.getFileTypeIcon('application/pdf')).toBe('📄');
        expect(DocumentService.getFileTypeIcon('application/vnd.openxmlformats-officedocument.wordprocessingml.document')).toBe('📝');
        expect(DocumentService.getFileTypeIcon('text/plain')).toBe('📃');
        expect(DocumentService.getFileTypeIcon('text/markdown')).toBe('📃');
        expect(DocumentService.getFileTypeIcon('unknown/type')).toBe('📄');
      });
    });
  });

  describe('updateDocumentStatus', () => {
    it('updates document status successfully', async () => {
      (updateDoc as any).mockResolvedValue(undefined);

      await DocumentService.updateDocumentStatus(mockDocumentId, 'completed');

      expect(updateDoc).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          status: 'completed',
          updatedAt: expect.anything(),
          processedAt: expect.anything()
        })
      );
    });

    it('includes error in update when provided', async () => {
      (updateDoc as any).mockResolvedValue(undefined);

      await DocumentService.updateDocumentStatus(mockDocumentId, 'failed', 'Processing error');

      expect(updateDoc).toHaveBeenCalledWith(
        expect.anything(),
        expect.objectContaining({
          status: 'failed',
          error: 'Processing error',
          updatedAt: expect.anything()
        })
      );
    });
  });

  describe('getDocumentChunks', () => {
    it('fetches document chunks successfully', async () => {
      const mockChunks = [
        { id: 'chunk1', data: () => ({ content: 'Chunk 1 content' }) },
        { id: 'chunk2', data: () => ({ content: 'Chunk 2 content' }) }
      ];

      const mockQuerySnapshot = { docs: mockChunks };
      (getDocs as any).mockResolvedValue(mockQuerySnapshot);

      const result = await DocumentService.getDocumentChunks(mockDocumentId);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({ id: 'chunk1', content: 'Chunk 1 content' });
    });
  });
});

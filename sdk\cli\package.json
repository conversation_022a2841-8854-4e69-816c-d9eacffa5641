{"name": "@rag-prompt-library/cli", "version": "1.0.0", "description": "Command-line interface for RAG Prompt Library", "main": "rag-cli.js", "bin": {"rag": "./rag-cli.js"}, "scripts": {"test": "jest", "build": "echo 'No build step required'", "prepublishOnly": "npm test"}, "keywords": ["rag", "prompt", "library", "cli", "ai", "llm", "command-line"], "author": "RAG Prompt Library Team", "license": "MIT", "dependencies": {"@rag-prompt-library/sdk": "^1.0.0", "commander": "^11.0.0", "chalk": "^4.1.2", "inquirer": "^8.2.5"}, "devDependencies": {"jest": "^29.0.0", "@types/jest": "^29.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/rag-prompt-library/sdk.git", "directory": "cli"}, "bugs": {"url": "https://github.com/rag-prompt-library/sdk/issues"}, "homepage": "https://docs.ragpromptlibrary.com/cli"}
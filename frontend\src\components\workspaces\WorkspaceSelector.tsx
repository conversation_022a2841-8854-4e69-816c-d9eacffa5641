import React, { useState, useEffect } from 'react';
import { 
  BuildingOfficeIcon, 
  ChevronDownIcon, 
  PlusIcon,
  UserGroupIcon,
  CogIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { functions } from '../../config/firebase';
import { httpsCallable } from 'firebase/functions';
import { useAuth } from '../../contexts/AuthContext';

interface Workspace {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  plan: string;
  member_count: number;
  user_role: string;
  created_at: any;
}

interface WorkspaceSelectorProps {
  selectedWorkspace: string | null;
  onWorkspaceSelect: (workspaceId: string | null) => void;
  onCreateWorkspace?: () => void;
}

const WorkspaceSelector: React.FC<WorkspaceSelectorProps> = ({
  selectedWorkspace,
  onWorkspaceSelect,
  onCreateWorkspace
}) => {
  const { currentUser } = useAuth();
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (currentUser) {
      loadWorkspaces();
    }
  }, [currentUser]);

  const loadWorkspaces = async () => {
    try {
      setLoading(true);
      const getUserWorkspaces = httpsCallable(functions, 'get_user_workspaces');
      const result = await getUserWorkspaces();
      const data = result.data as any;
      
      if (data.success) {
        setWorkspaces(data.workspaces);
        
        // Auto-select first workspace if none selected
        if (!selectedWorkspace && data.workspaces.length > 0) {
          onWorkspaceSelect(data.workspaces[0].id);
        }
      }
    } catch (error) {
      console.error('Error loading workspaces:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSelectedWorkspace = () => {
    if (!selectedWorkspace) return null;
    return workspaces.find(w => w.id === selectedWorkspace);
  };

  const getRoleColor = (role: string) => {
    const colors = {
      owner: 'bg-purple-100 text-purple-800',
      admin: 'bg-blue-100 text-blue-800',
      editor: 'bg-green-100 text-green-800',
      viewer: 'bg-gray-100 text-gray-800'
    };
    return colors[role as keyof typeof colors] || colors.viewer;
  };

  const getPlanColor = (plan: string) => {
    const colors = {
      free: 'bg-gray-100 text-gray-600',
      pro: 'bg-blue-100 text-blue-600',
      enterprise: 'bg-purple-100 text-purple-600'
    };
    return colors[plan as keyof typeof colors] || colors.free;
  };

  const selectedWorkspaceData = getSelectedWorkspace();

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded-md"></div>
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Workspace Selector Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center space-x-3">
          <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
          <div className="flex-1 min-w-0">
            {selectedWorkspaceData ? (
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-gray-900 truncate">
                    {selectedWorkspaceData.name}
                  </span>
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getRoleColor(selectedWorkspaceData.user_role)}`}>
                    {selectedWorkspaceData.user_role}
                  </span>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  <UserGroupIcon className="h-3 w-3 text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {selectedWorkspaceData.member_count} members
                  </span>
                  <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${getPlanColor(selectedWorkspaceData.plan)}`}>
                    {selectedWorkspaceData.plan}
                  </span>
                </div>
              </div>
            ) : (
              <div>
                <span className="text-gray-500">Personal Workspace</span>
                <div className="text-xs text-gray-400 mt-1">Individual prompts and documents</div>
              </div>
            )}
          </div>
        </div>
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <ChevronDownIcon className="h-5 w-5 text-gray-400" />
        </span>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-96 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
          {/* Personal Workspace Option */}
          <div
            onClick={() => {
              onWorkspaceSelect(null);
              setIsOpen(false);
            }}
            className="cursor-pointer select-none relative py-3 px-3 hover:bg-gray-50"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <BuildingOfficeIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <div className="font-medium text-gray-900">Personal Workspace</div>
                  <div className="text-xs text-gray-500">Individual prompts and documents</div>
                </div>
              </div>
              {!selectedWorkspace && (
                <CheckIcon className="h-5 w-5 text-blue-600" />
              )}
            </div>
          </div>

          {/* Divider */}
          {workspaces.length > 0 && (
            <div className="border-t border-gray-200 my-1"></div>
          )}

          {/* Team Workspaces */}
          {workspaces.map((workspace) => (
            <div
              key={workspace.id}
              onClick={() => {
                onWorkspaceSelect(workspace.id);
                setIsOpen(false);
              }}
              className="cursor-pointer select-none relative py-3 px-3 hover:bg-gray-50"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <UserGroupIcon className="h-5 w-5 text-blue-500" />
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900 truncate">
                        {workspace.name}
                      </span>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getRoleColor(workspace.user_role)}`}>
                        {workspace.user_role}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs text-gray-500">
                        {workspace.member_count} members
                      </span>
                      <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${getPlanColor(workspace.plan)}`}>
                        {workspace.plan}
                      </span>
                    </div>
                    {workspace.description && (
                      <div className="text-xs text-gray-400 mt-1 truncate">
                        {workspace.description}
                      </div>
                    )}
                  </div>
                </div>
                {selectedWorkspace === workspace.id && (
                  <CheckIcon className="h-5 w-5 text-blue-600" />
                )}
              </div>
            </div>
          ))}

          {/* Create New Workspace */}
          <div className="border-t border-gray-200 my-1"></div>
          <div
            onClick={() => {
              setIsOpen(false);
              if (onCreateWorkspace) {
                onCreateWorkspace();
              }
            }}
            className="cursor-pointer select-none relative py-3 px-3 hover:bg-blue-50 text-blue-600"
          >
            <div className="flex items-center space-x-3">
              <PlusIcon className="h-5 w-5" />
              <span className="font-medium">Create New Workspace</span>
            </div>
          </div>

          {/* Workspace Settings (if admin/owner) */}
          {selectedWorkspaceData && ['owner', 'admin'].includes(selectedWorkspaceData.user_role) && (
            <div
              onClick={() => {
                setIsOpen(false);
                // Navigate to workspace settings
              }}
              className="cursor-pointer select-none relative py-3 px-3 hover:bg-gray-50 text-gray-600"
            >
              <div className="flex items-center space-x-3">
                <CogIcon className="h-5 w-5" />
                <span className="font-medium">Workspace Settings</span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WorkspaceSelector;

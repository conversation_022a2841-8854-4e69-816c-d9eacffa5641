import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Prompt } from '../../types';

interface PromptCardProps {
  prompt: Prompt;
  onEdit?: (prompt: Prompt) => void;
  onDelete?: (promptId: string) => void;
  onExecute?: (prompt: Prompt) => void;
  onDuplicate?: (prompt: Prompt) => void;
  showActions?: boolean;
  className?: string;
}

export const PromptCard: React.FC<PromptCardProps> = ({
  prompt,
  onEdit,
  onDelete,
  onExecute,
  onDuplicate,
  showActions = true,
  className = ''
}) => {
  const navigate = useNavigate();

  const handleEdit = () => {
    if (onEdit) {
      onEdit(prompt);
    } else {
      navigate(`/prompts/${prompt.id}/edit`);
    }
  };

  const handleDelete = () => {
    if (onDelete && window.confirm('Are you sure you want to delete this prompt?')) {
      onDelete(prompt.id);
    }
  };

  const handleExecute = () => {
    if (onExecute) {
      onExecute(prompt);
    } else {
      navigate(`/prompts/${prompt.id}/execute`);
    }
  };

  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate(prompt);
    }
  };

  const handleCardClick = () => {
    navigate(`/prompts/${prompt.id}`);
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Unknown';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString();
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-shadow duration-200 cursor-pointer ${className}`}
      onClick={handleCardClick}
      data-testid="prompt-card"
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-lg font-semibold text-gray-900 truncate flex-1 mr-2">
            {prompt.title}
          </h3>
          {prompt.isPublic && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Public
            </span>
          )}
        </div>

        {/* Description */}
        {prompt.description && (
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {truncateText(prompt.description, 120)}
          </p>
        )}

        {/* Content Preview */}
        <div className="mb-4">
          <p className="text-gray-700 text-sm line-clamp-3">
            {truncateText(prompt.content, 150)}
          </p>
        </div>

        {/* Tags */}
        {prompt.tags && prompt.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {prompt.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {tag}
              </span>
            ))}
            {prompt.tags.length > 3 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                +{prompt.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Metadata */}
        <div className="flex justify-between items-center text-xs text-gray-500 mb-4">
          <span>Created: {formatDate(prompt.createdAt)}</span>
          <span>Updated: {formatDate(prompt.updatedAt)}</span>
        </div>

        {/* Variables Count */}
        {prompt.variables && prompt.variables.length > 0 && (
          <div className="text-xs text-gray-600 mb-3">
            Variables: {prompt.variables.length}
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex justify-end space-x-2 pt-3 border-t border-gray-100">
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleExecute();
              }}
              className="px-3 py-1 text-sm font-medium text-white bg-blue-600 rounded hover:bg-blue-700 transition-colors"
              data-testid="execute-button"
            >
              Execute
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleEdit();
              }}
              className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
              data-testid="edit-button"
            >
              Edit
            </button>
            {onDuplicate && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDuplicate();
                }}
                className="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                data-testid="duplicate-button"
              >
                Duplicate
              </button>
            )}
            {onDelete && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete();
                }}
                className="px-3 py-1 text-sm font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 transition-colors"
                data-testid="delete-button"
              >
                Delete
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PromptCard;
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'
  REGISTRY: gcr.io
  PROJECT_ID: ${{ secrets.GCP_PROJECT_ID }}

jobs:
  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: frontend

  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: 'pip'
      
      - name: Install dependencies
        run: |
          cd functions
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio
      
      - name: Run linting
        run: |
          cd functions
          flake8 src/ --max-line-length=100
          black --check src/
      
      - name: Run type checking
        run: |
          cd functions
          mypy src/ --ignore-missing-imports
      
      - name: Run tests
        env:
          REDIS_URL: redis://localhost:6379
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: |
          cd functions
          pytest tests/ -v --cov=src --cov-report=xml
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./functions/coverage.xml
          flags: backend

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'
      
      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Build and Push Images
  build:
    needs: [frontend-test, backend-test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Configure Docker to use gcloud as a credential helper
        run: gcloud auth configure-docker
      
      - name: Build and push backend image
        run: |
          docker build -t ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-backend:${{ github.sha }} ./functions
          docker push ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-backend:${{ github.sha }}
          docker tag ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-backend:${{ github.sha }} ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-backend:latest
          docker push ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-backend:latest
      
      - name: Build and push frontend image
        run: |
          docker build -f Dockerfile.frontend -t ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-frontend:${{ github.sha }} .
          docker push ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-frontend:${{ github.sha }}
          docker tag ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-frontend:${{ github.sha }} ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-frontend:latest
          docker push ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/rag-ai-frontend:latest

  # Deploy to Staging
  deploy-staging:
    needs: [build]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Get GKE credentials
        run: |
          gcloud container clusters get-credentials staging-cluster --zone us-central1-a
      
      - name: Deploy to staging
        run: |
          envsubst < k8s/staging/deployment.yaml | kubectl apply -f -
          kubectl rollout status deployment/rag-ai-backend -n staging
          kubectl rollout status deployment/rag-ai-frontend -n staging
        env:
          IMAGE_TAG: ${{ github.sha }}
      
      - name: Run smoke tests
        run: |
          kubectl wait --for=condition=ready pod -l app=rag-ai-backend -n staging --timeout=300s
          # Add smoke test commands here
          curl -f http://staging-api.example.com/health

  # Deploy to Production
  deploy-production:
    needs: [deploy-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v1
        with:
          service_account_key: ${{ secrets.GCP_SA_KEY }}
          project_id: ${{ env.PROJECT_ID }}
      
      - name: Get GKE credentials
        run: |
          gcloud container clusters get-credentials production-cluster --zone us-central1-a
      
      - name: Deploy to production
        run: |
          envsubst < k8s/production/deployment.yaml | kubectl apply -f -
          kubectl rollout status deployment/rag-ai-backend -n production
          kubectl rollout status deployment/rag-ai-frontend -n production
        env:
          IMAGE_TAG: ${{ github.sha }}
      
      - name: Run production health checks
        run: |
          kubectl wait --for=condition=ready pod -l app=rag-ai-backend -n production --timeout=300s
          curl -f https://api.example.com/health

  # Notify on completion
  notify:
    needs: [deploy-production]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        if: always()

import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { <PERSON>rowserRouter } from 'react-router-dom';
import AnalyticsDashboard from '../AnalyticsDashboard';

// Mock useAuth
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    currentUser: { uid: 'test-user-id' }
  }))
}));

// Mock useNavigate
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() => vi.fn())
  };
});

// Mock AnalyticsService
vi.mock('../../../services/analyticsService', () => ({
  AnalyticsService: {
    getUserAnalytics: vi.fn(() => Promise.resolve({
      totalPrompts: 10,
      totalExecutions: 50,
      totalTokens: 1000,
      totalCost: 5.25,
      executionsThisMonth: 15,
      tokensThisMonth: 300,
      costThisMonth: 1.50
    })),
    getUsageHistory: vi.fn(() => Promise.resolve([]))
  }
}));

// Mock Toast context
vi.mock('../../common/Toast', () => ({
  useToast: () => ({
    showToast: vi.fn()
  })
}));

describe('AnalyticsDashboard', () => {
  it('renders without crashing', () => {
    render(
      <BrowserRouter>
        <AnalyticsDashboard />
      </BrowserRouter>
    );
    // Component should render analytics content
    expect(document.body).toBeInTheDocument();
  });
});

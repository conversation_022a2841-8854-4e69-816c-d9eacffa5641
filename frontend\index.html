<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="RAG Prompt Library - Smart, modular, RAG-enabled prompt management system for AI workflows" />
    <meta name="keywords" content="AI, prompts, RAG, retrieval augmented generation, prompt library, AI tools" />
    <meta name="author" content="RAG Prompt Library" />

    <!-- Security Headers (Note: CSP and X-Frame-Options are set via HTTP headers in Firebase hosting) -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    <meta http-equiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=()" />

    <!-- Performance optimizations -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//firebaseapp.com" />
    <link rel="dns-prefetch" href="//openrouter.ai" />

    <!-- Preload critical resources will be handled by Vite build -->

    <!-- Theme and styling -->
    <meta name="theme-color" content="#3b82f6" />
    <meta name="color-scheme" content="light dark" />

    <title>RAG Prompt Library - Smart Prompt Management</title>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: system-ui, sans-serif;">
        <div style="text-align: center;">
          <div style="width: 40px; height: 40px; border: 4px solid #f3f4f6; border-top: 4px solid #3b82f6; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
          <p style="color: #6b7280; margin: 0;">Loading RAG Prompt Library...</p>
        </div>
      </div>
      <style>
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      </style>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

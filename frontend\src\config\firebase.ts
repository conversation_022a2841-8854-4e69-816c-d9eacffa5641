import { initializeApp } from 'firebase/app';
import { getAuth, connectAuthEmulator } from 'firebase/auth';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getStorage, connectStorageEmulator } from 'firebase/storage';
import { getFunctions, connectFunctionsEmulator } from 'firebase/functions';
import { getAnalytics } from 'firebase/analytics';

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID
};

// Validate required configuration
const requiredConfig = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
const missingConfig = requiredConfig.filter(key => !firebaseConfig[key as keyof typeof firebaseConfig]);

if (missingConfig.length > 0) {
  throw new Error(`Missing required Firebase configuration: ${missingConfig.join(', ')}`);
}

// Initialize Firebase
export const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
export const functions = getFunctions(app, 'australia-southeast1');

// Initialize Analytics (only in browser environment and when measurementId is available)
export const analytics = typeof window !== 'undefined' && firebaseConfig.measurementId
  ? getAnalytics(app)
  : null;

// Initialize App Check for additional security
if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_APP_CHECK === 'true') {
  import('./appCheck').then(({ initializeFirebaseAppCheck }) => {
    initializeFirebaseAppCheck();
  }).catch((error) => {
    console.warn('Failed to load App Check:', error);
  });
}

// Connect to Firebase Emulators in development
// TEMPORARILY DISABLED - Emulators not running, using demo Firebase configuration
// Only connect to emulators when running on localhost (development environment)
const ENABLE_EMULATORS = false; // Set to true when emulators are running

if (ENABLE_EMULATORS && import.meta.env.DEV && typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // Clear any previous connection flag to ensure fresh connection
  sessionStorage.removeItem('firebase-emulator-connected');

  try {
    console.log('🔧 Attempting to connect to Firebase Emulators...');

    // Connect to Auth Emulator
    connectAuthEmulator(auth, 'http://localhost:9100', { disableWarnings: true });
    console.log('✅ Auth Emulator connected on port 9100');

    // Connect to Firestore Emulator
    connectFirestoreEmulator(db, 'localhost', 8081);
    console.log('✅ Firestore Emulator connected on port 8081');

    // Connect to Functions Emulator
    connectFunctionsEmulator(functions, 'localhost', 5002);
    console.log('✅ Functions Emulator connected on port 5002');

    // Connect to Storage Emulator
    connectStorageEmulator(storage, 'localhost', 9200);
    console.log('✅ Storage Emulator connected on port 9200');

    // Mark emulators as connected to avoid reconnection attempts
    sessionStorage.setItem('firebase-emulator-connected', 'true');

    console.log('🎉 All Firebase Emulators connected successfully!');
  } catch (error) {
    console.error('❌ Emulator connection failed:', error);
    console.log('📡 Falling back to production Firebase services');
  }
} else {
  console.log('📡 Using demo Firebase configuration (emulators disabled)');
}

export default app;

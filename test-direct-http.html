<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct HTTP Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔧 Direct HTTP Function Test</h1>
    
    <div id="status" class="status info">
        Ready to test direct HTTP calls to Firebase Functions
    </div>
    
    <div>
        <button id="testDirectHTTP">Test Direct HTTP Call</button>
        <button id="testWithFetch">Test with Fetch API</button>
        <button id="clearResults">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const resultsDiv = document.getElementById('results');
        const testDirectHTTPBtn = document.getElementById('testDirectHTTP');
        const testWithFetchBtn = document.getElementById('testWithFetch');
        const clearResultsBtn = document.getElementById('clearResults');

        function addResult(title, content, isError = false) {
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${JSON.stringify(content, null, 2)}</pre>
                <small>Time: ${new Date().toLocaleTimeString()}</small>
            `;
            resultsDiv.appendChild(resultDiv);
        }

        // Test direct HTTP call to Firebase Function
        testDirectHTTPBtn.addEventListener('click', async () => {
            try {
                testDirectHTTPBtn.disabled = true;
                testDirectHTTPBtn.textContent = 'Testing...';
                
                const response = await fetch('https://us-central1-rag-prompt-library.cloudfunctions.net/execute_prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'https://rag-prompt-library.web.app'
                    },
                    body: JSON.stringify({
                        data: {
                            promptId: 'test-prompt',
                            inputs: { test: 'Hello World' },
                            useRag: false,
                            ragQuery: '',
                            documentIds: []
                        }
                    })
                });
                
                const data = await response.json();
                addResult('✅ Direct HTTP Call Success', {
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                });
                
            } catch (error) {
                addResult('❌ Direct HTTP Call Failed', {
                    message: error.message,
                    stack: error.stack
                }, true);
            } finally {
                testDirectHTTPBtn.disabled = false;
                testDirectHTTPBtn.textContent = 'Test Direct HTTP Call';
            }
        });

        // Test with Fetch API (simulating browser request)
        testWithFetchBtn.addEventListener('click', async () => {
            try {
                testWithFetchBtn.disabled = true;
                testWithFetchBtn.textContent = 'Testing...';
                
                // First try OPTIONS request
                const optionsResponse = await fetch('https://us-central1-rag-prompt-library.cloudfunctions.net/execute_prompt', {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': 'https://rag-prompt-library.web.app',
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                addResult('📋 OPTIONS Request', {
                    status: optionsResponse.status,
                    headers: Object.fromEntries(optionsResponse.headers.entries())
                });
                
                // Then try POST request
                const postResponse = await fetch('https://us-central1-rag-prompt-library.cloudfunctions.net/execute_prompt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'https://rag-prompt-library.web.app'
                    },
                    body: JSON.stringify({
                        data: {
                            promptId: 'test-prompt',
                            inputs: { test: 'Hello World' },
                            useRag: false
                        }
                    })
                });
                
                const postData = await postResponse.json();
                addResult('📤 POST Request', {
                    status: postResponse.status,
                    headers: Object.fromEntries(postResponse.headers.entries()),
                    data: postData
                });
                
            } catch (error) {
                addResult('❌ Fetch API Test Failed', {
                    message: error.message,
                    stack: error.stack
                }, true);
            } finally {
                testWithFetchBtn.disabled = false;
                testWithFetchBtn.textContent = 'Test with Fetch API';
            }
        });

        // Clear results
        clearResultsBtn.addEventListener('click', () => {
            resultsDiv.innerHTML = '';
        });
    </script>
</body>
</html>

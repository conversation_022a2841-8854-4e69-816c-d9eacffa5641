"""
Embedding Service - Generate embeddings for text chunks
"""
import os
import logging
import asyncio
import hashlib
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import json

# OpenAI import (conditional)
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Google Cloud AI Platform import (conditional)
try:
    from google.cloud import aiplatform
    from google.cloud.aiplatform.gapic.schema import predict
    import google.auth
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False

# Redis import for caching (conditional)
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class EmbeddingResult:
    text: str
    embedding: List[float]
    model: str
    dimensions: int
    tokens_used: int
    processing_time: float
    cached: bool = False

@dataclass
class BatchEmbeddingResult:
    results: List[EmbeddingResult]
    total_tokens: int
    total_time: float
    success_count: int
    error_count: int
    errors: List[str]

class EmbeddingService:
    """
    Service for generating text embeddings with caching and batch processing
    Supports both OpenAI and Google Cloud AI Platform embeddings
    """

    def __init__(self, provider: str = 'openai', api_key: str = None, redis_url: str = None,
                 google_project_id: str = None, google_location: str = 'us-central1'):
        self.provider = provider.lower()
        self.redis_url = redis_url or os.getenv('REDIS_URL')

        # Initialize embedding clients based on provider
        self.openai_client = None
        self.google_client = None

        if self.provider == 'openai':
            self.api_key = api_key or os.getenv('OPENROUTER_API_KEY')  # Use OpenRouter instead
            if self.api_key and OPENAI_AVAILABLE:
                self.openai_client = openai.OpenAI(
                    api_key=self.api_key,
                    base_url="https://openrouter.ai/api/v1"  # OpenRouter endpoint
                )
                logger.info("OpenAI embedding client initialized via OpenRouter")

        elif self.provider == 'google':
            self.google_project_id = google_project_id or os.getenv('GOOGLE_CLOUD_PROJECT')
            self.google_location = google_location
            self.google_api_key = api_key or os.getenv('GOOGLE_API_KEY')

            if GOOGLE_AVAILABLE and (self.google_project_id or self.google_api_key):
                try:
                    if self.google_project_id:
                        aiplatform.init(project=self.google_project_id, location=self.google_location)
                        self.google_client = aiplatform.gapic.PredictionServiceClient()
                        logger.info("Google Cloud AI Platform embedding client initialized")
                    else:
                        # Use API key for direct API calls
                        logger.info("Google API key configured for embeddings")
                except Exception as e:
                    logger.error(f"Failed to initialize Google embedding client: {e}")

        # Initialize Redis cache
        self.redis_client = None
        if self.redis_url and REDIS_AVAILABLE:
            try:
                self.redis_client = redis.from_url(self.redis_url, decode_responses=False)
                self.redis_client.ping()
                logger.info("Redis cache connected for embeddings")
            except Exception as e:
                logger.warning(f"Failed to connect to Redis cache: {e}")
                self.redis_client = None
        
        # Configuration
        if self.provider == 'google':
            self.default_model = 'text-embedding-004'
            self.batch_size = 100  # Google batch limit
        else:
            self.default_model = 'text-embedding-3-small'
            self.batch_size = 100  # OpenAI batch limit

        self.cache_ttl = 7 * 24 * 3600  # 7 days
        self.max_retries = 3
        self.retry_delay = 1.0

        # Model configurations
        self.model_configs = {
            # OpenAI models
            'text-embedding-3-small': {
                'provider': 'openai',
                'dimensions': 1536,
                'max_tokens': 8191,
                'cost_per_1k_tokens': 0.00002
            },
            'text-embedding-3-large': {
                'provider': 'openai',
                'dimensions': 3072,
                'max_tokens': 8191,
                'cost_per_1k_tokens': 0.00013
            },
            'text-embedding-ada-002': {
                'provider': 'openai',
                'dimensions': 1536,
                'max_tokens': 8191,
                'cost_per_1k_tokens': 0.0001
            },
            # Google models
            'text-embedding-004': {
                'provider': 'google',
                'dimensions': 768,
                'max_tokens': 2048,
                'cost_per_1k_tokens': 0.00001  # Estimated cost
            },
            'textembedding-gecko@003': {
                'provider': 'google',
                'dimensions': 768,
                'max_tokens': 3072,
                'cost_per_1k_tokens': 0.00001
            }
        }
    
    def _get_cache_key(self, text: str, model: str) -> str:
        """Generate cache key for text and model"""
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        return f"embedding:{model}:{text_hash}"
    
    def _get_cached_embedding(self, text: str, model: str) -> Optional[List[float]]:
        """Get cached embedding if available"""
        if not self.redis_client:
            return None
        
        try:
            cache_key = self._get_cache_key(text, model)
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                embedding = json.loads(cached_data)
                logger.debug(f"Cache hit for embedding: {cache_key}")
                return embedding
            
        except Exception as e:
            logger.warning(f"Cache read error: {e}")
        
        return None
    
    def _cache_embedding(self, text: str, model: str, embedding: List[float]):
        """Cache embedding for future use"""
        if not self.redis_client:
            return
        
        try:
            cache_key = self._get_cache_key(text, model)
            cached_data = json.dumps(embedding)
            
            self.redis_client.setex(
                cache_key, 
                self.cache_ttl, 
                cached_data
            )
            
            logger.debug(f"Cached embedding: {cache_key}")
            
        except Exception as e:
            logger.warning(f"Cache write error: {e}")
    
    def _estimate_tokens(self, text: str) -> int:
        """Estimate token count for text"""
        # Simple approximation: 1 token ≈ 4 characters
        return len(text) // 4
    
    def _validate_text(self, text: str, model: str) -> Tuple[bool, str]:
        """Validate text for embedding generation"""
        if not text or not text.strip():
            return False, "Text is empty"
        
        # Check token limit
        model_config = self.model_configs.get(model, self.model_configs[self.default_model])
        estimated_tokens = self._estimate_tokens(text)
        
        if estimated_tokens > model_config['max_tokens']:
            return False, f"Text too long: {estimated_tokens} tokens (max: {model_config['max_tokens']})"
        
        return True, ""
    
    async def generate_embedding(
        self, 
        text: str, 
        model: str = None
    ) -> Optional[EmbeddingResult]:
        """Generate embedding for a single text"""
        model = model or self.default_model
        start_time = time.time()
        
        # Validate text
        is_valid, error_msg = self._validate_text(text, model)
        if not is_valid:
            logger.error(f"Text validation failed: {error_msg}")
            return None
        
        # Check cache first
        cached_embedding = self._get_cached_embedding(text, model)
        if cached_embedding:
            processing_time = time.time() - start_time
            model_config = self.model_configs.get(model, self.model_configs[self.default_model])
            
            return EmbeddingResult(
                text=text,
                embedding=cached_embedding,
                model=model,
                dimensions=len(cached_embedding),
                tokens_used=self._estimate_tokens(text),
                processing_time=processing_time,
                cached=True
            )
        
        # Generate new embedding based on provider
        model_config = self.model_configs.get(model, self.model_configs[self.default_model])
        provider = model_config.get('provider', self.provider)

        try:
            if provider == 'openai':
                embedding, tokens_used = await self._generate_openai_embedding(text, model)
            elif provider == 'google':
                embedding, tokens_used = await self._generate_google_embedding(text, model)
            else:
                logger.error(f"Unsupported provider: {provider}")
                return None

            processing_time = time.time() - start_time

            # Cache the result
            self._cache_embedding(text, model, embedding)

            return EmbeddingResult(
                text=text,
                embedding=embedding,
                model=model,
                dimensions=len(embedding),
                tokens_used=tokens_used,
                processing_time=processing_time,
                cached=False
            )

        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return None

    async def _generate_openai_embedding(self, text: str, model: str) -> Tuple[List[float], int]:
        """Generate embedding using OpenAI API"""
        if not self.openai_client:
            raise Exception("OpenAI client not available")

        # Make API call with retries
        for attempt in range(self.max_retries):
            try:
                response = self.openai_client.embeddings.create(
                    input=text,
                    model=model
                )
                embedding = response.data[0].embedding
                tokens_used = response.usage.total_tokens
                return embedding, tokens_used
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                logger.warning(f"OpenAI embedding attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(self.retry_delay * (2 ** attempt))

    async def _generate_google_embedding(self, text: str, model: str) -> Tuple[List[float], int]:
        """Generate embedding using Google Cloud AI Platform API"""
        import requests

        if not self.google_api_key:
            raise Exception("Google API key not available")

        # Use Google Generative AI API for embeddings
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:embedContent"
        headers = {
            'Content-Type': 'application/json',
        }

        data = {
            'model': f'models/{model}',
            'content': {
                'parts': [{'text': text}]
            }
        }

        # Make API call with retries
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    url,
                    headers=headers,
                    json=data,
                    params={'key': self.google_api_key}
                )
                response.raise_for_status()

                result = response.json()
                embedding = result['embedding']['values']
                tokens_used = self._estimate_tokens(text)  # Google doesn't return token count

                return embedding, tokens_used
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                logger.warning(f"Google embedding attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(self.retry_delay * (2 ** attempt))

    async def generate_batch_embeddings(
        self, 
        texts: List[str], 
        model: str = None
    ) -> BatchEmbeddingResult:
        """Generate embeddings for multiple texts in batches"""
        model = model or self.default_model
        start_time = time.time()
        
        results = []
        errors = []
        total_tokens = 0
        
        # Process in batches
        for i in range(0, len(texts), self.batch_size):
            batch_texts = texts[i:i + self.batch_size]
            
            # Filter out invalid texts
            valid_texts = []
            for text in batch_texts:
                is_valid, error_msg = self._validate_text(text, model)
                if is_valid:
                    valid_texts.append(text)
                else:
                    errors.append(f"Text {i + len(valid_texts)}: {error_msg}")
            
            if not valid_texts:
                continue
            
            # Check cache for batch
            batch_results = []
            uncached_texts = []
            uncached_indices = []
            
            for idx, text in enumerate(valid_texts):
                cached_embedding = self._get_cached_embedding(text, model)
                if cached_embedding:
                    result = EmbeddingResult(
                        text=text,
                        embedding=cached_embedding,
                        model=model,
                        dimensions=len(cached_embedding),
                        tokens_used=self._estimate_tokens(text),
                        processing_time=0.0,
                        cached=True
                    )
                    batch_results.append(result)
                    total_tokens += result.tokens_used
                else:
                    uncached_texts.append(text)
                    uncached_indices.append(idx)
            
            # Generate embeddings for uncached texts
            if uncached_texts:
                model_config = self.model_configs.get(model, self.model_configs[self.default_model])
                provider = model_config.get('provider', self.provider)

                try:
                    if provider == 'openai' and self.openai_client:
                        # Make batch API call for OpenAI
                        response = self.openai_client.embeddings.create(
                            input=uncached_texts,
                            model=model
                        )

                        # Process results
                        for idx, (text, embedding_data) in enumerate(zip(uncached_texts, response.data)):
                            embedding = embedding_data.embedding

                            # Cache the result
                            self._cache_embedding(text, model, embedding)

                            result = EmbeddingResult(
                                text=text,
                                embedding=embedding,
                                model=model,
                                dimensions=len(embedding),
                                tokens_used=response.usage.total_tokens // len(uncached_texts),  # Approximate
                                processing_time=(time.time() - start_time) / len(uncached_texts),
                                cached=False
                            )
                            batch_results.append(result)
                            total_tokens += result.tokens_used

                    elif provider == 'google':
                        # Process Google embeddings one by one (no batch API)
                        for text in uncached_texts:
                            try:
                                embedding, tokens_used = await self._generate_google_embedding(text, model)

                                # Cache the result
                                self._cache_embedding(text, model, embedding)

                                result = EmbeddingResult(
                                    text=text,
                                    embedding=embedding,
                                    model=model,
                                    dimensions=len(embedding),
                                    tokens_used=tokens_used,
                                    processing_time=(time.time() - start_time) / len(uncached_texts),
                                    cached=False
                                )
                                batch_results.append(result)
                                total_tokens += result.tokens_used
                            except Exception as e:
                                error_msg = f"Google embedding failed for text: {e}"
                                logger.error(error_msg)
                                errors.append(error_msg)

                    else:
                        error_msg = f"No client available for provider: {provider}"
                        logger.error(error_msg)
                        errors.append(error_msg)

                except Exception as e:
                    error_msg = f"Batch embedding failed: {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            results.extend(batch_results)
        
        total_time = time.time() - start_time
        
        return BatchEmbeddingResult(
            results=results,
            total_tokens=total_tokens,
            total_time=total_time,
            success_count=len(results),
            error_count=len(errors),
            errors=errors
        )
    
    def get_model_info(self, model: str = None) -> Dict[str, Any]:
        """Get information about embedding models"""
        if model:
            return self.model_configs.get(model, {})
        else:
            return self.model_configs
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        if not self.redis_client:
            return {'cache_available': False}
        
        try:
            info = self.redis_client.info()
            return {
                'cache_available': True,
                'used_memory': info.get('used_memory_human', 'unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'cache_hits': info.get('keyspace_hits', 0),
                'cache_misses': info.get('keyspace_misses', 0)
            }
        except Exception as e:
            return {'cache_available': False, 'error': str(e)}
    
    def clear_cache(self, pattern: str = "embedding:*") -> bool:
        """Clear embedding cache"""
        if not self.redis_client:
            return False
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
                logger.info(f"Cleared {len(keys)} cached embeddings")
            return True
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")
            return False
    
    def is_available(self) -> bool:
        """Check if embedding service is available"""
        if self.provider == 'google':
            return GOOGLE_AVAILABLE and self.google_api_key is not None
        elif self.provider == 'openai':
            # Check for OpenRouter API key since we use OpenRouter for OpenAI embeddings
            return OPENAI_AVAILABLE and self.openai_client is not None
        return False

# Global instance - Use Google embeddings by default
embedding_service = EmbeddingService(provider='google')

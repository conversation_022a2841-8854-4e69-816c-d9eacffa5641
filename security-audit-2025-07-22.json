{"timestamp": "2025-07-22T05:05:51.286Z", "summary": {"total": 21, "passed": 6, "failed": 5, "warnings": 3}, "checks": [{"level": "INFO", "message": "Checking SSL/TLS configuration...", "details": null, "timestamp": "2025-07-22T05:05:51.290Z"}, {"level": "FAIL", "message": "SSL check failed: getaddrinfo ENOTFOUND app.ragpromptlibrary.com", "details": null, "timestamp": "2025-07-22T05:05:51.430Z"}, {"level": "INFO", "message": "Checking for dependency vulnerabilities...", "details": null, "timestamp": "2025-07-22T05:05:51.430Z"}, {"level": "PASS", "message": "No critical vulnerabilities in frontend dependencies", "details": null, "timestamp": "2025-07-22T05:05:52.849Z"}, {"level": "WARN", "message": "Could not run safety check for Python dependencies", "details": null, "timestamp": "2025-07-22T05:06:56.091Z"}, {"level": "INFO", "message": "Checking Firebase security rules...", "details": null, "timestamp": "2025-07-22T05:06:56.091Z"}, {"level": "PASS", "message": "Firestore rules require authentication", "details": null, "timestamp": "2025-07-22T05:06:56.091Z"}, {"level": "WARN", "message": "User-level data isolation may not be implemented", "details": null, "timestamp": "2025-07-22T05:06:56.091Z"}, {"level": "PASS", "message": "Storage rules require authentication", "details": null, "timestamp": "2025-07-22T05:06:56.092Z"}, {"level": "INFO", "message": "Checking API security headers...", "details": null, "timestamp": "2025-07-22T05:06:56.092Z"}, {"level": "WARN", "message": "API security check failed: getaddrinfo ENOTFOUND api.ragpromptlibrary.com", "details": null, "timestamp": "2025-07-22T05:06:56.160Z"}, {"level": "INFO", "message": "Checking environment variable security...", "details": null, "timestamp": "2025-07-22T05:06:56.160Z"}, {"level": "FAIL", "message": "Potential sensitive data found in D:\\react\\React-App-000730\\frontend\\dist\\assets\\index-DUpAO_b4.js", "details": null, "timestamp": "2025-07-22T05:06:56.168Z"}, {"level": "FAIL", "message": "Potential sensitive data found in D:\\react\\React-App-000730\\frontend\\src\\config\\firebase.ts", "details": null, "timestamp": "2025-07-22T05:06:56.172Z"}, {"level": "FAIL", "message": "Potential sensitive data found in D:\\react\\React-App-000730\\functions\\src\\security\\secrets_manager.py", "details": null, "timestamp": "2025-07-22T05:06:56.215Z"}, {"level": "FAIL", "message": "Potential sensitive data found in D:\\react\\React-App-000730\\scripts\\production_verification.js", "details": null, "timestamp": "2025-07-22T05:06:56.231Z"}, {"level": "PASS", "message": "No sensitive data found in source code", "details": null, "timestamp": "2025-07-22T05:06:56.234Z"}, {"level": "INFO", "message": "Checking input validation...", "details": null, "timestamp": "2025-07-22T05:06:56.234Z"}, {"level": "INFO", "message": "Checking data encryption...", "details": null, "timestamp": "2025-07-22T05:06:56.239Z"}, {"level": "PASS", "message": "Firebase provides encryption at rest", "details": null, "timestamp": "2025-07-22T05:06:56.240Z"}, {"level": "PASS", "message": "Data in transit encrypted via HTTPS", "details": null, "timestamp": "2025-07-22T05:06:56.240Z"}]}
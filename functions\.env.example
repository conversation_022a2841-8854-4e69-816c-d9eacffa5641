# RAG Prompt Library - Backend Environment Configuration Template
# Copy this file to .env and fill in your actual values

# OpenRouter API Configuration
# Get your API keys from https://openrouter.ai/keys
# OpenRouter handles all LLM operations including embeddings
OPENROUTER_API_KEY=sk-or-v1-your-primary-api-key-here
OPENROUTER_API_KEY_RAG=sk-or-v1-your-rag-specific-api-key-here

# Model Configuration
# Free models available on OpenRouter
PROMPT_GENERATION_MODEL=nvidia/llama-3.1-nemotron-ultra-253b-v1:free
RAG_PROCESSING_MODEL=nvidia/llama-3.1-nemotron-ultra-253b-v1:free
DEFAULT_EMBEDDING_MODEL=text-embedding-3-small
DEFAULT_LLM_MODEL=anthropic/claude-3.5-sonnet

# Alternative paid models (uncomment to use)
# PROMPT_GENERATION_MODEL=anthropic/claude-3.5-sonnet
# RAG_PROCESSING_MODEL=openai/gpt-4-turbo-preview

# Environment Configuration
PYTHON_ENV=development
NODE_ENV=production
FUNCTIONS_EMULATOR=false
LOG_LEVEL=info

# Firebase Project Configuration (usually auto-configured)
FIREBASE_PROJECT_ID=rag-prompt-library
FIREBASE_STORAGE_BUCKET=rag-prompt-library.firebasestorage.app

# API Configuration
API_VERSION=v1
MAX_CONTEXT_CHUNKS=10
API_RATE_LIMIT=100
API_RATE_WINDOW=60

# LLM Configuration
LLM_TIMEOUT=30
LLM_MAX_RETRIES=3
LLM_RETRY_DELAY=1

# RAG Configuration
VECTOR_DIMENSION=1536
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_CHUNKS_PER_QUERY=10
SIMILARITY_THRESHOLD=0.7

# Document Processing
MAX_DOCUMENT_SIZE=10485760
ALLOWED_DOCUMENT_TYPES=pdf,txt,doc,docx,md
PROCESSING_TIMEOUT=300

# Security Configuration
JWT_SECRET=your-jwt-secret-here
API_KEY_PREFIX=rag_
ENABLE_RATE_LIMITING=true
ENABLE_API_KEY_AUTH=true

# Feature Flags
ENABLE_MULTI_MODEL_SUPPORT=true
ENABLE_ADVANCED_RAG=true
ENABLE_ANALYTICS=true
ENABLE_AUDIT_LOGGING=true

# Monitoring and Logging
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_REPORTING=true
SENTRY_DSN=your-sentry-dsn-here

# Production Overrides (set these in production)
# PYTHON_ENV=production
# NODE_ENV=production
# FUNCTIONS_EMULATOR=false
# LOG_LEVEL=warn

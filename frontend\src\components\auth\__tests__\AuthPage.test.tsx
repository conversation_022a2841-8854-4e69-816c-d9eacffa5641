import React from 'react';
import { screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AuthPage } from '../AuthPage';
import { useAuth } from '../../../contexts/AuthContext';
import { renderWithProviders, waitForAsyncUpdates, actAsync, waitForCondition } from '../../../test/test-utils';

// Mock auth context
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn()
}));

const mockUseAuth = useAuth as vi.MockedFunction<typeof useAuth>;

describe('AuthPage', () => {
  const mockLogin = vi.fn();
  const mockSignup = vi.fn();
  const mockLoginWithGoogle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseAuth.mockReturnValue({
      currentUser: null,
      userProfile: null,
      loading: false,
      login: mockLogin,
      signup: mockSignup,
      logout: vi.fn(),
      loginWithGoogle: mockLoginWithGoogle,
      updateUserProfile: vi.fn()
    });
  });

  it('renders sign in form by default', () => {
    renderWithProviders(<AuthPage />);

    expect(screen.getByRole('heading', { name: 'Sign In' })).toBeInTheDocument();
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('switches to sign up form', () => {
    renderWithProviders(<AuthPage />);

    const signUpButton = screen.getByRole('button', { name: /sign up/i });
    act(() => { fireEvent.click(signUpButton); });

    expect(screen.getByRole('heading', { name: 'Create Account' })).toBeInTheDocument();
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
  });

  it('validates email format', async () => {
    renderWithProviders(<AuthPage />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText('Password');
    const form = emailInput.closest('form');

    await actAsync(async () => {
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.submit(form!);
    });

    await waitFor(() => {
      expect(screen.getByText(/invalid email format/i)).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('validates password length', async () => {
    renderWithProviders(<AuthPage />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText('Password');
    const form = emailInput.closest('form');

    await actAsync(async () => {
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.submit(form!);
    });

    await waitFor(() => {
      expect(screen.getByText(/password must be at least 6 characters/i)).toBeInTheDocument();
    });

    expect(mockLogin).not.toHaveBeenCalled();
  });

  it('validates password confirmation in sign up', async () => {
    renderWithProviders(<AuthPage />);

    // Switch to sign up
    const signUpToggleButton = screen.getByRole('button', { name: /sign up/i });
    act(() => { fireEvent.click(signUpToggleButton); });

    const displayNameInput = screen.getByLabelText(/full name/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    const form = emailInput.closest('form');

    act(() => { fireEvent.change(displayNameInput, { target: { value: 'Test User' } }); });
    act(() => { fireEvent.change(emailInput, { target: { value: '<EMAIL>' } }); });
    act(() => { fireEvent.change(passwordInput, { target: { value: 'password123' } }); });
    act(() => { fireEvent.change(confirmPasswordInput, { target: { value: 'different123' } }); });
    act(() => { fireEvent.submit(form!); });

    await waitFor(() => {
      expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
    });

    expect(mockSignup).not.toHaveBeenCalled();
  });

  it('calls signIn with valid credentials', async () => {
    mockLogin.mockResolvedValue(undefined);

    renderWithProviders(<AuthPage />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText('Password');
    const signInButton = screen.getByRole('button', { name: /sign in/i });

    await actAsync(async () => {
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
    });

    await actAsync(async () => {
      fireEvent.click(signInButton);
    });

    await waitForCondition(() =>
      mockLogin.mock.calls.length > 0
    );

    expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
  });

  it('calls signUp with valid data', async () => {
    mockSignup.mockResolvedValue(undefined);

    renderWithProviders(<AuthPage />);

    // Switch to sign up
    const signUpToggleButton = screen.getByRole('button', { name: /sign up/i });
    act(() => { fireEvent.click(signUpToggleButton); });

    const displayNameInput = screen.getByLabelText(/full name/i);
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/^password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
    const form = emailInput.closest('form');

    act(() => { fireEvent.change(displayNameInput, { target: { value: 'Test User' } }); });
    act(() => { fireEvent.change(emailInput, { target: { value: '<EMAIL>' } }); });
    act(() => { fireEvent.change(passwordInput, { target: { value: 'password123' } }); });
    act(() => { fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } }); });
    act(() => { fireEvent.submit(form!); });

    await waitFor(() => {
      expect(mockSignup).toHaveBeenCalledWith('<EMAIL>', 'password123', 'Test User');
    });
  });

  it('calls signInWithGoogle when Google button is clicked', async () => {
    mockLoginWithGoogle.mockResolvedValue(undefined);

    renderWithProviders(<AuthPage />);

    const googleButton = screen.getByRole('button', { name: /continue with google/i });
    act(() => { fireEvent.click(googleButton); });

    await waitFor(() => {
      expect(mockLoginWithGoogle).toHaveBeenCalled();
    });
  });

  it('shows loading state during authentication', async () => {
    mockLogin.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    renderWithProviders(<AuthPage />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText('Password');
    const signInButton = screen.getByRole('button', { name: /sign in/i });

    await actAsync(async () => {
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(signInButton);
    });

    expect(screen.getByText(/signing in/i)).toBeInTheDocument();
    expect(signInButton).toBeDisabled();

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalled();
    });
  });

  it('handles authentication errors', async () => {
    mockLogin.mockRejectedValue(new Error('Invalid credentials'));

    renderWithProviders(<AuthPage />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText('Password');
    const signInButton = screen.getByRole('button', { name: /sign in/i });

    await actAsync(async () => {
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(signInButton);
    });

    await waitFor(() => {
      expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
    });
  });

  it('shows loading state from auth context', () => {
    mockUseAuth.mockReturnValue({
      currentUser: null,
      userProfile: null,
      loading: true,
      login: mockLogin,
      signup: mockSignup,
      logout: vi.fn(),
      loginWithGoogle: mockLoginWithGoogle,
      updateUserProfile: vi.fn()
    });

    renderWithProviders(<AuthPage />);

    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('toggles password visibility', () => {
    renderWithProviders(<AuthPage />);

    const passwordInput = screen.getByLabelText('Password');
    const toggleButton = screen.getByRole('button', { name: /toggle password visibility/i });

    expect(passwordInput).toHaveAttribute('type', 'password');

    act(() => { fireEvent.click(toggleButton); });
    expect(passwordInput).toHaveAttribute('type', 'text');

    act(() => { fireEvent.click(toggleButton); });
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('clears form when switching between sign in and sign up', async () => {
    renderWithProviders(<AuthPage />);

    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText('Password');

    // Fill in sign in form
    await actAsync(async () => {
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
    });

    // Switch to sign up
    const signUpButton = screen.getByRole('button', { name: /sign up/i });
    await actAsync(async () => {
      fireEvent.click(signUpButton);
    });

    // Form should be cleared
    expect(screen.getByLabelText(/email/i)).toHaveValue('');
    expect(screen.getByLabelText(/^password/i)).toHaveValue('');
  });

  it('handles Google sign in errors', async () => {
    mockLoginWithGoogle.mockRejectedValue(new Error('Google sign in failed'));

    renderWithProviders(<AuthPage />);

    const googleButton = screen.getByRole('button', { name: /continue with google/i });
    act(() => { fireEvent.click(googleButton); });

    await waitFor(() => {
      expect(screen.getByText(/google sign in failed/i)).toBeInTheDocument();
    });
  });
});

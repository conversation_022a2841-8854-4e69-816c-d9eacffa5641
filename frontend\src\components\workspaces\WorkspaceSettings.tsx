import React, { useState, useEffect } from 'react';
import {
  CogIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  BellIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { useWorkspace } from '../../contexts/WorkspaceContext';
import { useAuth } from '../../contexts/AuthContext';
import { MemberManagement } from './MemberManagement';

interface WorkspaceSettingsProps {
  workspaceId: string;
  onClose: () => void;
}

export const WorkspaceSettings: React.FC<WorkspaceSettingsProps> = ({
  workspaceId,
  onClose
}) => {
  const { user } = useAuth();
  const {
    currentWorkspace,
    updateWorkspace,
    deleteWorkspace,
    getUserRole,
    canUserManage
  } = useWorkspace();

  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [settings, setSettings] = useState({
    name: '',
    description: '',
    isPublic: false,
    allowPublicPrompts: true,
    requireApproval: false,
    allowGuestAccess: false,
    enableNotifications: true,
    enableComments: true,
    enableVersionHistory: true,
    retentionDays: 365
  });

  const userRole = getUserRole(workspaceId);
  const canManage = canUserManage(workspaceId);

  useEffect(() => {
    if (currentWorkspace && currentWorkspace.id === workspaceId) {
      setSettings({
        name: currentWorkspace.name,
        description: currentWorkspace.description,
        isPublic: currentWorkspace.isPublic,
        allowPublicPrompts: currentWorkspace.settings?.allowPublicPrompts ?? true,
        requireApproval: currentWorkspace.settings?.requireApproval ?? false,
        allowGuestAccess: currentWorkspace.settings?.allowGuestAccess ?? false,
        enableNotifications: true,
        enableComments: true,
        enableVersionHistory: true,
        retentionDays: 365
      });
    }
  }, [currentWorkspace, workspaceId]);

  const handleSaveSettings = async () => {
    if (!canManage) return;

    setLoading(true);
    try {
      await updateWorkspace(workspaceId, {
        name: settings.name,
        description: settings.description,
        isPublic: settings.isPublic,
        settings: {
          allowPublicPrompts: settings.allowPublicPrompts,
          requireApproval: settings.requireApproval,
          allowGuestAccess: settings.allowGuestAccess
        }
      });

      // Show success message
      alert('Settings saved successfully!');
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteWorkspace = async () => {
    if (!canManage || userRole !== 'owner') return;

    setLoading(true);
    try {
      await deleteWorkspace(workspaceId);
      onClose();
      // Navigate to workspaces list
    } catch (error) {
      console.error('Failed to delete workspace:', error);
      alert('Failed to delete workspace. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', name: 'General', icon: CogIcon },
    { id: 'members', name: 'Members', icon: UserGroupIcon },
    { id: 'permissions', name: 'Permissions', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'danger', name: 'Danger Zone', icon: ExclamationTriangleIcon }
  ];

  if (!currentWorkspace || currentWorkspace.id !== workspaceId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-gray-500">Loading workspace settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              Workspace Settings
            </h3>
            <p className="text-sm text-gray-500">
              Manage your workspace configuration and preferences
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {/* General Tab */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Workspace Name
                </label>
                <input
                  type="text"
                  value={settings.name}
                  onChange={(e) => setSettings({ ...settings, name: e.target.value })}
                  disabled={!canManage}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={settings.description}
                  onChange={(e) => setSettings({ ...settings, description: e.target.value })}
                  disabled={!canManage}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPublic"
                  checked={settings.isPublic}
                  onChange={(e) => setSettings({ ...settings, isPublic: e.target.checked })}
                  disabled={!canManage}
                  className="mr-2"
                />
                <label htmlFor="isPublic" className="text-sm text-gray-700">
                  Make workspace publicly discoverable
                </label>
              </div>

              {canManage && (
                <div className="flex justify-end">
                  <button
                    onClick={handleSaveSettings}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <CheckIcon className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Members Tab */}
          {activeTab === 'members' && (
            <MemberManagement
              workspaceId={workspaceId}
              userRole={userRole || 'viewer'}
              onMemberUpdate={() => {
                // Refresh workspace data if needed
              }}
            />
          )}

          {/* Permissions Tab */}
          {activeTab === 'permissions' && (
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">
                  Workspace Permissions
                </h4>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Allow public prompt sharing
                      </label>
                      <p className="text-xs text-gray-500">
                        Members can share prompts publicly outside the workspace
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.allowPublicPrompts}
                      onChange={(e) => setSettings({ ...settings, allowPublicPrompts: e.target.checked })}
                      disabled={!canManage}
                      className="ml-2"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Require approval for sharing
                      </label>
                      <p className="text-xs text-gray-500">
                        All shared prompts must be approved by an admin
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.requireApproval}
                      onChange={(e) => setSettings({ ...settings, requireApproval: e.target.checked })}
                      disabled={!canManage}
                      className="ml-2"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Allow guest access
                      </label>
                      <p className="text-xs text-gray-500">
                        Non-members can view public prompts in this workspace
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.allowGuestAccess}
                      onChange={(e) => setSettings({ ...settings, allowGuestAccess: e.target.checked })}
                      disabled={!canManage}
                      className="ml-2"
                    />
                  </div>
                </div>
              </div>

              {canManage && (
                <div className="flex justify-end">
                  <button
                    onClick={handleSaveSettings}
                    disabled={loading}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <CheckIcon className="h-4 w-4 mr-2" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">
                  Notification Preferences
                </h4>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Email notifications
                      </label>
                      <p className="text-xs text-gray-500">
                        Receive email updates about workspace activity
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.enableNotifications}
                      onChange={(e) => setSettings({ ...settings, enableNotifications: e.target.checked })}
                      className="ml-2"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Comment notifications
                      </label>
                      <p className="text-xs text-gray-500">
                        Get notified when someone comments on your prompts
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.enableComments}
                      onChange={(e) => setSettings({ ...settings, enableComments: e.target.checked })}
                      className="ml-2"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Version history
                      </label>
                      <p className="text-xs text-gray-500">
                        Track changes and maintain version history
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      checked={settings.enableVersionHistory}
                      onChange={(e) => setSettings({ ...settings, enableVersionHistory: e.target.checked })}
                      disabled={!canManage}
                      className="ml-2"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Danger Zone Tab */}
          {activeTab === 'danger' && userRole === 'owner' && (
            <div className="space-y-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      Danger Zone
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <p>
                        Once you delete a workspace, there is no going back. Please be certain.
                      </p>
                    </div>
                    <div className="mt-4">
                      <button
                        onClick={() => setShowDeleteConfirm(true)}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <TrashIcon className="h-4 w-4 mr-2" />
                        Delete Workspace
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-60">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                  <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
                </div>
                <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4">
                  Delete Workspace
                </h3>
                <div className="mt-2 px-7 py-3">
                  <p className="text-sm text-gray-500">
                    Are you sure you want to delete "{currentWorkspace?.name}"? This action cannot be undone.
                    All prompts, documents, and member data will be permanently removed.
                  </p>
                </div>
                <div className="items-center px-4 py-3">
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleDeleteWorkspace}
                      disabled={loading}
                      className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 disabled:opacity-50"
                    >
                      {loading ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkspaceSettings;
{"timestamp": "2025-07-22T19:40:41.753Z", "summary": {"total": 11, "passed": 9, "failed": 1, "warnings": 1}, "successRate": 81.8, "results": {"apiEndpoints": [{"category": "apiEndpoints", "name": "test_cors function", "status": "FAIL", "timestamp": "2025-07-22T19:40:39.312Z", "responseTime": 793, "details": "HTTP 400", "error": "{\"error\":{\"message\":\"Bad Request\",\"status\":\"INVALID_ARGUMENT\"}}\n"}, {"category": "apiEndpoints", "name": "generate_prompt auth check", "status": "WARN", "timestamp": "2025-07-22T19:40:39.626Z", "responseTime": 313, "details": "Expected 401/403, got 400", "error": null}, {"category": "apiEndpoints", "name": "execute_prompt auth check", "status": "PASS", "timestamp": "2025-07-22T19:40:40.024Z", "responseTime": 396, "details": "Authentication properly enforced", "error": null}, {"category": "apiEndpoints", "name": "CORS for https://rag-prompt-library.web.app", "status": "PASS", "timestamp": "2025-07-22T19:40:40.328Z", "responseTime": 303, "details": "CORS headers present", "error": null}, {"category": "apiEndpoints", "name": "CORS for http://localhost:5173", "status": "PASS", "timestamp": "2025-07-22T19:40:40.630Z", "responseTime": 301, "details": "CORS headers present", "error": null}, {"category": "apiEndpoints", "name": "CORS for http://localhost:3000", "status": "PASS", "timestamp": "2025-07-22T19:40:40.941Z", "responseTime": 310, "details": "CORS headers present", "error": null}], "ragPipeline": [], "integration": [{"category": "integration", "name": "Web app availability", "status": "PASS", "timestamp": "2025-07-22T19:40:38.515Z", "responseTime": 638, "details": "Application accessible", "error": null}, {"category": "integration", "name": "Web app content", "status": "PASS", "timestamp": "2025-07-22T19:40:38.517Z", "responseTime": 638, "details": "Application content loaded", "error": null}], "performance": [{"category": "performance", "name": "Web app load time", "status": "PASS", "timestamp": "2025-07-22T19:40:41.142Z", "responseTime": 199, "details": "Within threshold (3000ms)", "error": null}, {"category": "performance", "name": "CORS preflight time", "status": "PASS", "timestamp": "2025-07-22T19:40:41.442Z", "responseTime": 299, "details": "Within threshold (1000ms)", "error": null}, {"category": "performance", "name": "Function cold start", "status": "PASS", "timestamp": "2025-07-22T19:40:41.751Z", "responseTime": 309, "details": "Within threshold (5000ms)", "error": null}], "summary": {"total": 11, "passed": 9, "failed": 1, "warnings": 1}}}
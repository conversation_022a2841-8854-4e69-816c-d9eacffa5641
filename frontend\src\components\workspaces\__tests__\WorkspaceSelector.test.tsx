import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import WorkspaceSelector from '../WorkspaceSelector';

// Mock useAuth
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    currentUser: { uid: 'test-user-id' }
  }))
}));

// Mock useNavigate
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() => vi.fn())
  };
});

// Mock WorkspaceService
vi.mock('../../../services/workspaceService', () => ({
  WorkspaceService: {
    subscribeToUserWorkspaces: vi.fn(() => () => {}), // Return unsubscribe function
    createWorkspace: vi.fn(() => Promise.resolve()),
    switchWorkspace: vi.fn(() => Promise.resolve())
  }
}));

// Mock Toast context
vi.mock('../../common/Toast', () => ({
  useToast: () => ({
    showToast: vi.fn()
  })
}));

describe('WorkspaceSelector', () => {
  it('renders without crashing', () => {
    const mockOnWorkspaceSelect = vi.fn();

    render(
      <BrowserRouter>
        <WorkspaceSelector
          selectedWorkspace={null}
          onWorkspaceSelect={mockOnWorkspaceSelect}
        />
      </BrowserRouter>
    );
    // Component should render some workspace-related content
    expect(document.body).toBeDefined();
  });
});

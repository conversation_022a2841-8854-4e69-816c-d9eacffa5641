# Firebase Configuration - Template
# Replace these with your actual Firebase project values
VITE_FIREBASE_API_KEY=your_firebase_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789012
VITE_FIREBASE_APP_ID=1:123456789012:web:abcdef123456
VITE_FIREBASE_MEASUREMENT_ID=G-ABCDEF1234

# Development Environment
# Set to true to use Firebase emulators for local development
VITE_USE_EMULATORS=false

# API Configuration
# OpenRouter API for LLM integration
VITE_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Application Configuration
VITE_APP_NAME=RAG Prompt Library
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=production

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Debug Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info

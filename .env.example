# RAG Prompt Library - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# ===================================
# FIREBASE CONFIGURATION
# ===================================
# Get these values from Firebase Console > Project Settings > General > Your apps
FIREBASE_API_KEY=your_firebase_api_key_here
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_STORAGE_BUCKET=your-project.firebasestorage.app
FIREBASE_MESSAGING_SENDER_ID=123456789012
FIREBASE_APP_ID=1:123456789012:web:abcdef123456
FIREBASE_MEASUREMENT_ID=G-ABCDEF1234

# ===================================
# API CONFIGURATION
# ===================================
# LLM Provider API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
COHERE_API_KEY=your_cohere_api_key_here

# OpenRouter API for AI model access
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_KEY_RAG=your_openrouter_rag_api_key_here

# Vector Database Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=us-east-1-aws
PINECONE_INDEX_NAME=rag-prompt-library

# Redis Configuration
REDIS_URL=redis://localhost:6379

# ===================================
# DEPLOYMENT CONFIGURATION
# ===================================
# Firebase deployment token (for CI/CD)
FIREBASE_TOKEN=your_firebase_deployment_token

# GitHub deployment token
GITHUB_TOKEN=your_github_token

# ===================================
# APPLICATION CONFIGURATION
# ===================================
# Environment (development, staging, production)
NODE_ENV=development

# Application domain
APP_DOMAIN=localhost:3000

# ===================================
# SECURITY CONFIGURATION
# ===================================
# Encryption keys (generate secure random strings)
ENCRYPTION_KEY=your_32_character_encryption_key_here
JWT_SECRET=your_jwt_secret_key_here

# ===================================
# MONITORING CONFIGURATION
# ===================================
# Enable monitoring in production
MONITORING_ENABLED=false

# Sentry DSN for error tracking
SENTRY_DSN=https://<EMAIL>/project-id

# ===================================
# THIRD-PARTY INTEGRATIONS
# ===================================
# Google Analytics
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX

# Stripe (for payments)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# ===================================
# DATABASE CONFIGURATION
# ===================================
# Firestore settings
FIRESTORE_EMULATOR_HOST=localhost:8080

# ===================================
# DEVELOPMENT CONFIGURATION
# ===================================
# Local development ports
FRONTEND_PORT=5173
BACKEND_PORT=5001
FUNCTIONS_PORT=5001

# ===================================
# PRODUCTION OVERRIDES
# ===================================
# For production deployment, set these values:
# NODE_ENV=production
# MONITORING_ENABLED=true
# APP_DOMAIN=your-production-domain.com

# ===================================
# SETUP INSTRUCTIONS
# ===================================
# 1. Copy this file to .env
# 2. Replace all placeholder values with your actual configuration
# 3. Never commit .env to version control
# 4. For production, use secure environment variable management
# 5. Restart your services after making changes

# ===================================
# SECURITY NOTES
# ===================================
# - Keep your API keys secure and never expose them publicly
# - Use different keys for development, staging, and production
# - Regularly rotate API keys and secrets
# - Monitor usage and set up billing alerts
# - Enable Firebase security rules for production
# - Use HTTPS in production environments

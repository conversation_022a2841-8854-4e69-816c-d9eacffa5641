#!/usr/bin/env python3
"""
Simple health check script to test Firebase Functions
"""
import requests
import json

def test_system_status():
    """Test the AI system status endpoint"""
    url = "http://127.0.0.1:5002/react-app-000730-default/australia-southeast1/get_ai_system_status"
    
    try:
        # Test with POST request (Firebase callable functions expect POST)
        response = requests.post(url, json={"data": {}}, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing system status: {e}")
        return False

def test_openrouter_connection():
    """Test the OpenRouter connection endpoint"""
    url = "http://127.0.0.1:5002/react-app-000730-default/australia-southeast1/test_openrouter_connection"
    
    try:
        response = requests.post(url, json={"data": {}}, timeout=10)
        print(f"OpenRouter Status Code: {response.status_code}")
        print(f"OpenRouter Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error testing OpenRouter: {e}")
        return False

if __name__ == "__main__":
    print("Testing Firebase Functions Health...")
    print("=" * 50)
    
    print("\n1. Testing AI System Status:")
    status_ok = test_system_status()
    
    print("\n2. Testing OpenRouter Connection:")
    openrouter_ok = test_openrouter_connection()
    
    print("\n" + "=" * 50)
    print("Health Check Summary:")
    print(f"AI System Status: {'✅ PASS' if status_ok else '❌ FAIL'}")
    print(f"OpenRouter Connection: {'✅ PASS' if openrouter_ok else '❌ FAIL'}")
    
    if status_ok and openrouter_ok:
        print("\n🎉 All health checks passed!")
    else:
        print("\n⚠️ Some health checks failed!")

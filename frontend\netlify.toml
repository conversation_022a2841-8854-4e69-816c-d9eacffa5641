# Netlify configuration for React RAG Application

[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "20"
  NPM_VERSION = "10"

# Redirects and rewrites
[[redirects]]
  from = "/api/*"
  to = "https://your-api-domain.com/api/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for caching and security
[[headers]]
  for = "/assets/js/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    Vary = "Accept-Encoding"

[[headers]]
  for = "/assets/css/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
    Vary = "Accept-Encoding"

[[headers]]
  for = "/assets/images/*"
  [headers.values]
    Cache-Control = "public, max-age=2592000"
    Vary = "Accept-Encoding"

[[headers]]
  for = "/assets/fonts/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000"
    Access-Control-Allow-Origin = "*"

[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache, no-store, must-revalidate"

[[headers]]
  for = "/manifest.json"
  [headers.values]
    Cache-Control = "public, max-age=86400"

[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://*.firebaseapp.com https://*.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.firebaseapp.com https://*.googleapis.com wss://*.firebaseapp.com; frame-src 'self' https://accounts.google.com;"

# Compression
[[headers]]
  for = "*.js"
  [headers.values]
    Content-Encoding = "gzip"

[[headers]]
  for = "*.css"
  [headers.values]
    Content-Encoding = "gzip"

# Edge functions (if using Netlify Edge Functions)
[[edge_functions]]
  function = "cache-headers"
  path = "/assets/*"

# Build plugins
[[plugins]]
  package = "@netlify/plugin-lighthouse"
  
  [plugins.inputs]
    output_path = "lighthouse"
    
    [[plugins.inputs.audits]]
      url = "/"
      thresholds.performance = 0.9
      thresholds.accessibility = 0.9
      thresholds.best-practices = 0.9
      thresholds.seo = 0.9

# Environment-specific settings
[context.production]
  command = "npm run build:optimized"
  
  [context.production.environment]
    NODE_ENV = "production"
    VITE_CDN_URL = "https://cdn.your-domain.com"

[context.deploy-preview]
  command = "npm run build"
  
  [context.deploy-preview.environment]
    NODE_ENV = "development"

[context.branch-deploy]
  command = "npm run build"

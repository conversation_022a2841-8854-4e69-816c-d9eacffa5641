{"timestamp": "2025-07-22T10:47:06.870323", "total_endpoints": 31, "passed": 0, "failed": 0, "warnings": 31, "endpoints": [{"category": "Core Prompt Management", "method": "GET", "path": "/prompts", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/prompts", "auth_required": true, "status": "warning", "response_time": 1314.5, "status_code": 404, "error": null}, {"category": "Core Prompt Management", "method": "POST", "path": "/prompts", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/prompts", "auth_required": true, "status": "warning", "response_time": 1202.87, "status_code": 404, "error": null}, {"category": "Core Prompt Management", "method": "GET", "path": "/prompts/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/prompts/test-id", "auth_required": true, "status": "warning", "response_time": 1216.72, "status_code": 404, "error": null}, {"category": "Core Prompt Management", "method": "PUT", "path": "/prompts/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/prompts/test-id", "auth_required": true, "status": "warning", "response_time": 1199.66, "status_code": 404, "error": null}, {"category": "Core Prompt Management", "method": "DELETE", "path": "/prompts/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/prompts/test-id", "auth_required": true, "status": "warning", "response_time": 1239.34, "status_code": 404, "error": null}, {"category": "Core Prompt Management", "method": "POST", "path": "/prompts/{id}/execute", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/prompts/test-id/execute", "auth_required": true, "status": "warning", "response_time": 1225.91, "status_code": 404, "error": null}, {"category": "Document Management", "method": "GET", "path": "/documents", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/documents", "auth_required": true, "status": "warning", "response_time": 1227.47, "status_code": 404, "error": null}, {"category": "Document Management", "method": "POST", "path": "/documents", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/documents", "auth_required": true, "status": "warning", "response_time": 1234.67, "status_code": 404, "error": null}, {"category": "Document Management", "method": "GET", "path": "/documents/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/documents/test-id", "auth_required": true, "status": "warning", "response_time": 1230.3, "status_code": 404, "error": null}, {"category": "Document Management", "method": "DELETE", "path": "/documents/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/documents/test-id", "auth_required": true, "status": "warning", "response_time": 1179.54, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "GET", "path": "/workspaces", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces", "auth_required": true, "status": "warning", "response_time": 1304.79, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "POST", "path": "/workspaces", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces", "auth_required": true, "status": "warning", "response_time": 1193.63, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "GET", "path": "/workspaces/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces/test-id", "auth_required": true, "status": "warning", "response_time": 1214.15, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "PUT", "path": "/workspaces/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces/test-id", "auth_required": true, "status": "warning", "response_time": 1229.73, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "DELETE", "path": "/workspaces/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces/test-id", "auth_required": true, "status": "warning", "response_time": 1228.1, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "POST", "path": "/workspaces/{id}/members", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces/test-id/members", "auth_required": true, "status": "warning", "response_time": 1198.34, "status_code": 404, "error": null}, {"category": "Team Workspaces", "method": "DELETE", "path": "/workspaces/{id}/members/{user_id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/workspaces/test-id/members/test-user-id", "auth_required": true, "status": "warning", "response_time": 1206.61, "status_code": 404, "error": null}, {"category": "Analytics Dashboard", "method": "GET", "path": "/analytics/dashboard", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/analytics/dashboard", "auth_required": true, "status": "warning", "response_time": 1208.85, "status_code": 404, "error": null}, {"category": "Analytics Dashboard", "method": "GET", "path": "/analytics/metrics", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/analytics/metrics", "auth_required": true, "status": "warning", "response_time": 1238.13, "status_code": 404, "error": null}, {"category": "Analytics Dashboard", "method": "GET", "path": "/analytics/usage", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/analytics/usage", "auth_required": true, "status": "warning", "response_time": 1189.8, "status_code": 404, "error": null}, {"category": "Analytics Dashboard", "method": "GET", "path": "/analytics/performance", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/analytics/performance", "auth_required": true, "status": "warning", "response_time": 1193.63, "status_code": 404, "error": null}, {"category": "Template Marketplace", "method": "GET", "path": "/marketplace/templates", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/marketplace/templates", "auth_required": false, "status": "warning", "response_time": 1197.14, "status_code": 404, "error": null}, {"category": "Template Marketplace", "method": "POST", "path": "/marketplace/templates", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/marketplace/templates", "auth_required": true, "status": "warning", "response_time": 1241.27, "status_code": 404, "error": null}, {"category": "Template Marketplace", "method": "GET", "path": "/marketplace/templates/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/marketplace/templates/test-id", "auth_required": false, "status": "warning", "response_time": 1194.98, "status_code": 404, "error": null}, {"category": "Template Marketplace", "method": "PUT", "path": "/marketplace/templates/{id}", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/marketplace/templates/test-id", "auth_required": true, "status": "warning", "response_time": 1224.38, "status_code": 404, "error": null}, {"category": "Template Marketplace", "method": "POST", "path": "/marketplace/templates/{id}/rate", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/marketplace/templates/test-id/rate", "auth_required": true, "status": "warning", "response_time": 1216.31, "status_code": 404, "error": null}, {"category": "RAG Processing", "method": "POST", "path": "/rag/query", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/rag/query", "auth_required": true, "status": "warning", "response_time": 1218.17, "status_code": 404, "error": null}, {"category": "RAG Processing", "method": "POST", "path": "/rag/embed", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/rag/embed", "auth_required": true, "status": "warning", "response_time": 1228.47, "status_code": 404, "error": null}, {"category": "RAG Processing", "method": "GET", "path": "/rag/status", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/rag/status", "auth_required": true, "status": "warning", "response_time": 1214.72, "status_code": 404, "error": null}, {"category": "System", "method": "GET", "path": "/health", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/health", "auth_required": false, "status": "warning", "response_time": 1213.93, "status_code": 404, "error": null}, {"category": "System", "method": "GET", "path": "/version", "url": "https://us-central1-rag-prompt-library.cloudfunctions.net/api/v1/version", "auth_required": false, "status": "warning", "response_time": 1230.96, "status_code": 404, "error": null}]}
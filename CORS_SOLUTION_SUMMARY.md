# CORS Issue Solution Summary

## Problem Identified
You're experiencing CORS (Cross-Origin Resource Sharing) errors when uploading PDF files to Firebase Storage:

```
Access to XMLHttpRequest at 'https://firebasestorage.googleapis.com/...' from origin 'https://rag-prompt-library.web.app' has been blocked by CORS policy: Response to preflight request doesn't pass access control check: It does not have HTTP ok status.
```

## Root Cause
Firebase Storage buckets don't have CORS configured by default, which blocks direct uploads from web applications.

## Solutions Provided

### Solution 1: Fix Firebase Storage CORS (Recommended for Production)

**Files Updated:**
- `cors.json` - Enhanced with comprehensive CORS configuration
- `scripts/fix-storage-cors.sh` - <PERSON>sh script to apply CORS
- `scripts/fix-storage-cors.ps1` - PowerShell script for Windows
- `scripts/fix-storage-cors.js` - Node.js script alternative

**Steps to Apply:**
1. Install Google Cloud SDK: https://cloud.google.com/sdk/docs/install
2. Authenticate: `gcloud auth login`
3. Set project: `gcloud config set project rag-prompt-library`
4. Apply CORS: `gsutil cors set cors.json gs://rag-prompt-library.appspot.com`

**Quick Command:**
```bash
gsutil cors set cors.json gs://rag-prompt-library.appspot.com
```

### Solution 2: Firebase Function Upload (Immediate Workaround)

**Files Created:**
- `functions/main.py` - Added `upload_document_via_function`
- `frontend/src/components/documents/DocumentUploadFunction.tsx` - Alternative upload component
- `scripts/deploy-upload-function.sh` - Deployment script

**How it Works:**
1. Frontend converts file to base64
2. Sends data to Firebase Function via HTTPS callable
3. Function uploads to Storage using Admin SDK (bypasses CORS)
4. Returns download URL and document metadata

**To Deploy:**
```bash
chmod +x scripts/deploy-upload-function.sh
./scripts/deploy-upload-function.sh
```

## Immediate Action Plan

### Option A: Quick Fix (Use Function Upload)
1. Deploy the new Firebase Function:
   ```bash
   npx firebase deploy --only functions:upload_document_via_function
   ```

2. Update your upload component to use `DocumentUploadFunction`:
   ```tsx
   import { DocumentUploadFunction } from './components/documents/DocumentUploadFunction';
   
   // Replace DocumentUpload with DocumentUploadFunction
   <DocumentUploadFunction onUploadComplete={handleUploadComplete} />
   ```

### Option B: Proper Fix (Configure Storage CORS)
1. Install Google Cloud SDK
2. Run: `gsutil cors set cors.json gs://rag-prompt-library.appspot.com`
3. Wait 2-3 minutes for propagation
4. Clear browser cache and test

## Updated CORS Configuration

The `cors.json` now includes:
- All your domains (production + development)
- All necessary HTTP methods
- Comprehensive response headers
- Proper cache settings

```json
[
  {
    "origin": [
      "https://rag-prompt-library.web.app",
      "https://rag-prompt-library.firebaseapp.com",
      "http://localhost:3000",
      "http://localhost:5173",
      "http://127.0.0.1:5000"
    ],
    "method": ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS"],
    "maxAgeSeconds": 3600,
    "responseHeader": [
      "Content-Type",
      "Authorization", 
      "Content-Length",
      "User-Agent",
      "X-Requested-With",
      "Access-Control-Allow-Origin",
      "Access-Control-Allow-Methods",
      "Access-Control-Allow-Headers",
      "Access-Control-Max-Age"
    ]
  }
]
```

## Testing Steps

1. **Clear browser cache** (important!)
2. **Try uploading a PDF** using either method
3. **Check browser console** for errors
4. **Monitor Firebase Function logs** if using function upload:
   ```bash
   npx firebase functions:log --only upload_document_via_function
   ```

## Advantages of Each Solution

### Storage CORS Fix:
- ✅ Direct uploads (faster)
- ✅ Better for large files
- ✅ Standard approach
- ❌ Requires Google Cloud SDK setup

### Function Upload:
- ✅ Works immediately
- ✅ No additional tools needed
- ✅ Bypasses all CORS issues
- ❌ Slower for large files (base64 encoding)
- ❌ Function timeout limits (5 minutes)

## Recommendation

1. **Immediate**: Use the Function Upload method to get uploads working right away
2. **Long-term**: Set up proper Storage CORS for better performance

## Troubleshooting

If you still experience issues:

1. **Check authentication**: Ensure you're logged into the correct Google account
2. **Verify permissions**: You need Storage Admin role on the project
3. **Clear cache**: Browser cache can cause persistent CORS errors
4. **Wait for propagation**: CORS changes can take a few minutes
5. **Check function logs**: Monitor for any function execution errors

## Support Files

- `CORS_FIX_GUIDE.md` - Detailed step-by-step guide
- `scripts/fix-storage-cors.*` - Automated CORS setup scripts
- `DocumentUploadFunction.tsx` - Alternative upload component

## Next Steps

1. Choose your preferred solution (Function upload for immediate fix, or Storage CORS for long-term)
2. Deploy and test the solution
3. Monitor for any remaining issues
4. Update documentation for your team

The function upload method should work immediately and bypass all CORS issues!

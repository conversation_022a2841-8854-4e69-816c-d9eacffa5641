apiVersion: v1
kind: Namespace
metadata:
  name: production
  labels:
    name: production
    environment: production

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-ai-backend
  namespace: production
  labels:
    app: rag-ai-backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rag-ai-backend
  template:
    metadata:
      labels:
        app: rag-ai-backend
        version: v1
    spec:
      serviceAccountName: rag-ai-backend
      containers:
      - name: backend
        image: gcr.io/${PROJECT_ID}/rag-ai-backend:${IMAGE_TAG}
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: redis-url
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: openai-api-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: anthropic-api-key
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: google-api-key
        - name: COHERE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: cohere-api-key
        - name: PINECONE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: pinecone-api-key
        - name: PINECONE_ENVIRONMENT
          valueFrom:
            secretKeyRef:
              name: rag-ai-secrets
              key: pinecone-environment
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-ai-frontend
  namespace: production
  labels:
    app: rag-ai-frontend
    version: v1
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: rag-ai-frontend
  template:
    metadata:
      labels:
        app: rag-ai-frontend
        version: v1
    spec:
      containers:
      - name: frontend
        image: gcr.io/${PROJECT_ID}/rag-ai-frontend:${IMAGE_TAG}
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: REACT_APP_API_URL
          value: "https://api.example.com"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          runAsNonRoot: true
          runAsUser: 1000

---
apiVersion: v1
kind: Service
metadata:
  name: rag-ai-backend-service
  namespace: production
  labels:
    app: rag-ai-backend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: rag-ai-backend

---
apiVersion: v1
kind: Service
metadata:
  name: rag-ai-frontend-service
  namespace: production
  labels:
    app: rag-ai-frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: rag-ai-frontend

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rag-ai-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.example.com
    - app.example.com
    secretName: rag-ai-tls
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rag-ai-backend-service
            port:
              number: 80
  - host: app.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rag-ai-frontend-service
            port:
              number: 80

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rag-ai-backend-hpa
  namespace: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rag-ai-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: rag-ai-backend
  namespace: production
  annotations:
    iam.gke.io/gcp-service-account: rag-ai-backend@${PROJECT_ID}.iam.gserviceaccount.com

---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: rag-ai-network-policy
  namespace: production
spec:
  podSelector:
    matchLabels:
      app: rag-ai-backend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: nginx-ingress
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 6379

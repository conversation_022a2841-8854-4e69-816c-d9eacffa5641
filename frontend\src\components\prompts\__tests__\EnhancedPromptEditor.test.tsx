
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor, act } from '@testing-library/react';
import { renderWithProviders } from '../../../test/test-utils';
import { EnhancedPromptEditor } from '../EnhancedPromptEditor';
import type { Prompt } from '../../../types';

// Mock the template service
vi.mock('../../../services/templateService', () => ({
  templateService: {
    getTemplates: vi.fn().mockResolvedValue([]),
    getCategories: vi.fn().mockResolvedValue([]),
    incrementUsage: vi.fn().mockResolvedValue(undefined)
  }
}));

const mockPrompt: Prompt = {
  id: '1',
  title: 'Test Prompt',
  content: 'This is a test prompt with {{variable}}',
  description: 'A test prompt for testing',
  tags: ['test'],
  category: 'General',
  isPublic: false,
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: 'user1',
  version: 1,
  variables: [
    {
      name: 'variable',
      type: 'string',
      description: 'A test variable',
      required: true
    }
  ]
};

const mockOnSave = vi.fn();
const mockOnExecute = vi.fn();

describe('EnhancedPromptEditor', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders enhanced prompt editor', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    expect(screen.getByText('Basic Information')).toBeInTheDocument();
    expect(screen.getByText('Template Library')).toBeInTheDocument();
    expect(screen.getByText('Quality Analysis')).toBeInTheDocument();
  });

  test('loads existing prompt data', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        prompt={mockPrompt}
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    expect(screen.getByDisplayValue('Test Prompt')).toBeInTheDocument();
    expect(screen.getByDisplayValue('A test prompt for testing')).toBeInTheDocument();
    expect(screen.getByDisplayValue('This is a test prompt with {{variable}}')).toBeInTheDocument();
  });

  test('handles title input change', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const titleInput = screen.getByPlaceholderText('Enter a descriptive title for your prompt');
    act(() => { fireEvent.change(titleInput, { target: { value: 'New Prompt Title' } }); });

    expect(titleInput).toHaveValue('New Prompt Title');
  });

  test('handles description input change', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const descriptionInput = screen.getByPlaceholderText('Describe what this prompt does and when to use it');
    act(() => { fireEvent.change(descriptionInput, { target: { value: 'New description' } }); });

    expect(descriptionInput).toHaveValue('New description');
  });

  test('handles category selection', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const categorySelect = screen.getByLabelText(/Category/i);
    act(() => { fireEvent.change(categorySelect, { target: { value: 'Content Creation' } }); });

    expect(categorySelect).toHaveValue('Content Creation');
  });

  test('handles public checkbox toggle', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const publicCheckbox = screen.getByLabelText(/Make public/i);
    act(() => { fireEvent.click(publicCheckbox); });

    expect(publicCheckbox).toBeChecked();
  });

  test('opens template library when button clicked', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const templateLibraryButton = screen.getByText('Template Library');
    act(() => { fireEvent.click(templateLibraryButton); });

    // Template library modal should open
    // Note: This would need to be tested with proper modal rendering
  });

  test('toggles preview mode', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const previewButton = screen.getByText('Preview');
    act(() => { fireEvent.click(previewButton); });

    expect(screen.getByText('Edit')).toBeInTheDocument();
  });

  test('calls onSave when save button clicked with valid data', async () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText('Enter a descriptive title for your prompt');
    const contentTextarea = screen.getByPlaceholderText(/Enter your prompt content here/);

    act(() => { fireEvent.change(titleInput, { target: { value: 'Test Title' } }); });
    act(() => { fireEvent.change(contentTextarea, { target: { value: 'Test content' } }); });

    const saveButton = screen.getByText('Save Prompt');
    act(() => { fireEvent.click(saveButton); });

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith({
        title: 'Test Title',
        content: 'Test content',
        description: '',
        category: '',
        tags: [],
        isPublic: false,
        variables: []
      });
    });
  });

  test('calls onExecute when test button clicked', async () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    // Fill in required fields
    const titleInput = screen.getByPlaceholderText('Enter a descriptive title for your prompt');
    const contentTextarea = screen.getByPlaceholderText(/Enter your prompt content here/);

    act(() => { fireEvent.change(titleInput, { target: { value: 'Test Title' } }); });
    act(() => { fireEvent.change(contentTextarea, { target: { value: 'Test content' } }); });

    const testButton = screen.getByText('Test Prompt');
    act(() => { fireEvent.click(testButton); });

    await waitFor(() => {
      expect(mockOnExecute).toHaveBeenCalledWith({
        title: 'Test Title',
        content: 'Test content',
        description: '',
        category: '',
        tags: [],
        isPublic: false,
        variables: []
      });
    });
  });

  test('disables save button when required fields are empty', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const saveButton = screen.getByText('Save Prompt');
    expect(saveButton).toBeDisabled();
  });

  test('enables save button when required fields are filled', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    const titleInput = screen.getByPlaceholderText('Enter a descriptive title for your prompt');
    const contentTextarea = screen.getByPlaceholderText(/Enter your prompt content here/);

    act(() => { fireEvent.change(titleInput, { target: { value: 'Test Title' } }); });
    act(() => { fireEvent.change(contentTextarea, { target: { value: 'Test content' } }); });

    const saveButton = screen.getByText('Save Prompt');
    expect(saveButton).not.toBeDisabled();
  });

  test('shows loading state when loading prop is true', () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
        loading={true}
      />
    );

    // Should show loading spinner in save button
    expect(screen.getByText('Save Prompt')).toBeInTheDocument();
  });

  test('displays quality score when available', async () => {
    renderWithProviders(
      <EnhancedPromptEditor
        onSave={mockOnSave}
        onExecute={mockOnExecute}
      />
    );

    // Add content to trigger quality analysis
    const contentTextarea = screen.getByPlaceholderText(/Enter your prompt content here/);
    act(() => { fireEvent.change(contentTextarea, { target: { value: 'This is a test prompt with good structure and clear instructions.' } }); });

    // Wait for quality analysis to complete (mocked)
    await waitFor(() => {
      // Quality score should be displayed
      expect(screen.getByText('Quality Analysis')).toBeInTheDocument();
    }, { timeout: 2000 });
  });
});

describe('EnhancedPromptEditor Integration', () => {
  test('template selection updates form fields', () => {
    // This would test the integration between the editor and template library
    // In a real implementation, this would involve more complex interaction testing
  });

  test('quality analyzer suggestions can be applied', () => {
    // This would test the integration between the editor and quality analyzer
    // Testing auto-fix functionality and suggestion application
  });

  test('variable editor updates are reflected in content preview', () => {
    // This would test the integration between variable editor and content preview
    // Ensuring that variable changes update the preview correctly
  });
});

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to RAG Prompt Library Beta!</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        h1 {
            color: #1a202c;
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .celebration {
            font-size: 48px;
            margin: 20px 0;
        }
        .status-badge {
            display: inline-block;
            background-color: #d4edda;
            color: #155724;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            margin: 20px 0;
        }
        .content {
            margin: 30px 0;
        }
        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background-color: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        .feature-card h4 {
            margin: 10px 0 5px;
            color: #2d3748;
            font-size: 16px;
        }
        .feature-card p {
            margin: 0;
            color: #718096;
            font-size: 14px;
        }
        .getting-started {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        .getting-started h3 {
            margin-top: 0;
            font-size: 20px;
        }
        .getting-started ol {
            text-align: left;
            max-width: 400px;
            margin: 20px auto;
        }
        .getting-started li {
            margin: 10px 0;
            padding-left: 10px;
        }
        .cta-button {
            display: inline-block;
            background-color: white;
            color: #667eea;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 10px;
            border: 2px solid white;
        }
        .cta-button:hover {
            background-color: rgba(255, 255, 255, 0.9);
        }
        .secondary-button {
            display: inline-block;
            background-color: transparent;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            margin: 20px 10px;
            border: 2px solid white;
        }
        .limits-info {
            background-color: #f0fff4;
            border: 1px solid #9ae6b4;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .limits-info h4 {
            color: #22543d;
            margin-top: 0;
        }
        .limits-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }
        .limit-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }
        .limit-value {
            font-weight: 600;
            color: #22543d;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
            color: #718096;
            font-size: 14px;
        }
        .support-info {
            background-color: #fef5e7;
            border: 1px solid #f6ad55;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .support-info h4 {
            color: #c05621;
            margin-top: 0;
        }
        @media (max-width: 600px) {
            .features-grid {
                grid-template-columns: 1fr;
            }
            .limits-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">RP</div>
            <div class="celebration">🎉</div>
            <h1>Welcome to Beta!</h1>
            <div class="status-badge">Beta Access Approved</div>
        </div>

        <div class="content">
            <p>Hi {{name}},</p>
            
            <p><strong>Congratulations!</strong> Your application for the RAG Prompt Library Beta Program has been approved. We're thrilled to have you join our exclusive beta community!</p>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h4>Early Access</h4>
                    <p>Get new features weeks before public release</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h4>Advanced RAG</h4>
                    <p>Access to cutting-edge retrieval capabilities</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🚀</div>
                    <h4>Priority Support</h4>
                    <p>Direct line to our engineering team</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">💡</div>
                    <h4>Shape the Future</h4>
                    <p>Your feedback drives product development</p>
                </div>
            </div>

            <div class="getting-started">
                <h3>Getting Started</h3>
                <ol>
                    <li>Log in to your account using the button below</li>
                    <li>Explore the beta features in your dashboard</li>
                    <li>Upload your first documents for RAG</li>
                    <li>Create and execute your first prompts</li>
                    <li>Join our beta community Discord</li>
                </ol>
                
                <a href="{{loginUrl}}" class="cta-button">Access Beta Dashboard</a>
                <a href="{{discordUrl}}" class="secondary-button">Join Discord</a>
            </div>

            <div class="limits-info">
                <h4>Your Beta Limits</h4>
                <p>As a beta user, you have access to generous limits:</p>
                <div class="limits-grid">
                    <div class="limit-item">
                        <span>Monthly Executions:</span>
                        <span class="limit-value">10,000</span>
                    </div>
                    <div class="limit-item">
                        <span>Monthly Tokens:</span>
                        <span class="limit-value">1,000,000</span>
                    </div>
                    <div class="limit-item">
                        <span>Max Documents:</span>
                        <span class="limit-value">1,000</span>
                    </div>
                    <div class="limit-item">
                        <span>Max Workspaces:</span>
                        <span class="limit-value">10</span>
                    </div>
                </div>
            </div>

            <div class="support-info">
                <h4>Beta Support & Feedback</h4>
                <p>We're here to help you succeed! Here's how to get support and share feedback:</p>
                <ul>
                    <li><strong>Priority Support:</strong> Email us at <a href="mailto:<EMAIL>" style="color: #c05621;"><EMAIL></a></li>
                    <li><strong>Community:</strong> Join our <a href="{{discordUrl}}" style="color: #c05621;">Discord community</a> for real-time help</li>
                    <li><strong>Feedback:</strong> Use the feedback widget in your dashboard</li>
                    <li><strong>Bug Reports:</strong> Report issues directly through the platform</li>
                </ul>
            </div>

            <p>We're excited to see what you'll build with RAG Prompt Library. Your feedback and insights will be invaluable in shaping the future of our platform.</p>

            <p>Welcome aboard!</p>
            
            <p>
                Best regards,<br>
                <strong>The RAG Prompt Library Team</strong>
            </p>
        </div>

        <div class="footer">
            <p>
                RAG Prompt Library Beta Program<br>
                <a href="{{supportUrl}}" style="color: #718096;">Support</a> | 
                <a href="{{docsUrl}}" style="color: #718096;">Documentation</a> |
                <a href="{{discordUrl}}" style="color: #718096;">Discord</a>
            </p>
            
            <p style="font-size: 12px; color: #a0aec0;">
                This email was sent to {{email}}. Beta access is valid for the duration of the beta program.
            </p>
        </div>
    </div>
</body>
</html>

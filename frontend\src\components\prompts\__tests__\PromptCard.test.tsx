import React from 'react';
import { screen, fireEvent, act, cleanup } from '@testing-library/react';
import { renderWithProviders } from '../../../test/test-utils';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { PromptCard } from '../PromptCard';
import { <PERSON>rowserRouter } from 'react-router-dom';

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn()
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <>
    {children}
  </>
);

afterEach(() => {
    cleanup();
  });

  describe('PromptCard', () => {
  const mockPrompt = {
    id: 'test-prompt-id',
    title: 'Test Prompt',
    content: 'This is a test prompt with {{variable1}} and {{variable2}}',
    variables: [
      { name: 'variable1', type: 'text' as const, description: 'First variable', required: true },
      { name: 'variable2', type: 'number' as const, description: 'Second variable', required: false }
    ],
    tags: ['test', 'example'],
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02'),
    userId: 'test-user'
  };

  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();
  const mockOnExecute = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders prompt card with basic information', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    expect(screen.getAllByText('Test Prompt')[0]).toBeInTheDocument();
    expect(screen.getAllByText(/This is a test prompt/)[0]).toBeInTheDocument();
    expect(screen.getAllByText('test')[0]).toBeInTheDocument();
    expect(screen.getAllByText('example')[0]).toBeInTheDocument();
  });

  it('shows variable count', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    expect(screen.getAllByText('Variables: 2')[0]).toBeInTheDocument();
  });

  it('shows creation date', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    expect(screen.getAllByText(/1\/1\/2023/)[0]).toBeInTheDocument();
  });

  it('calls onExecute when execute button is clicked', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    const executeButton = screen.getByRole('button', { name: /execute/i });
    act(() => { fireEvent.click(executeButton); });

    expect(mockOnExecute).toHaveBeenCalledWith(mockPrompt);
  });

  it('calls onEdit when edit button is clicked', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    const editButton = screen.getByRole('button', { name: /edit/i });
    act(() => { fireEvent.click(editButton); });

    expect(mockOnEdit).toHaveBeenCalledWith(mockPrompt);
  });

  it('calls onDelete when delete button is clicked', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    act(() => { fireEvent.click(deleteButton); });

    expect(mockOnDelete).toHaveBeenCalledWith(mockPrompt.id);
  });

  it('truncates long content', () => {
    const longPrompt = {
      ...mockPrompt,
      content: 'This is a very long prompt content that should be truncated when displayed in the card view because it exceeds the maximum length that we want to show in the preview.'
    };

    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={longPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    const content = screen.getAllByText(/This is a very long prompt/)[0];
    expect(content.textContent).toHaveLength(153); // 150 chars + "..." = 153
  });

  it('handles prompt with no variables', () => {
    const noVariablesPrompt = {
      ...mockPrompt,
      variables: []
    };

    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={noVariablesPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    expect(screen.queryByText(/Variables:/)).not.toBeInTheDocument();
  });

  it('handles prompt with no tags', () => {
    const noTagsPrompt = {
      ...mockPrompt,
      tags: []
    };

    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={noTagsPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    // Should not show any tag elements
    expect(screen.queryByText('test')).not.toBeInTheDocument();
    expect(screen.queryByText('example')).not.toBeInTheDocument();
  });

  it('shows hover effects on card', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    const card = screen.getByTestId('prompt-card');
    expect(card).toHaveClass('hover:shadow-lg');
  });

  it('displays correct variable types', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    // Check if variable information is displayed (might be in a tooltip or expanded view)
    const card = screen.getAllByText('Test Prompt')[0].closest('div');
    act(() => { fireEvent.mouseEnter(card!); });

    // Variables might be shown in a tooltip or expanded state
    expect(screen.getAllByText('Variables: 2')[0]).toBeInTheDocument();
  });

  it('handles missing optional props gracefully', () => {
    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={mockPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    // Should render without errors even if some optional props are missing
    expect(screen.getAllByText('Test Prompt')[0]).toBeInTheDocument();
  });

  it('shows updated date when different from created date', () => {
    const updatedPrompt = {
      ...mockPrompt,
      updatedAt: new Date('2023-01-10')
    };

    renderWithProviders(
      <TestWrapper>
        <PromptCard 
          prompt={updatedPrompt}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onExecute={mockOnExecute}
        />
      </TestWrapper>
    );

    // Should show updated date instead of created date
    expect(screen.getAllByText(/1\/10\/2023/)[0]).toBeInTheDocument();
  });
});

#!/usr/bin/env python3
"""
Error Handling and Logging Verification Script
Tests error handling mechanisms and logging across the application
"""
import requests
import json
import time
from typing import Dict, List

def test_frontend_error_handling() -> Dict[str, bool]:
    """Test frontend error handling"""
    results = {}
    
    print("Testing Frontend Error Handling...")
    
    # Test 404 error handling
    try:
        response = requests.get('http://localhost:3000/nonexistent-page', timeout=10)
        # Frontend should handle 404s gracefully (usually returns 200 with React Router)
        results["404 Handling"] = response.status_code in [200, 404]
        print(f"✅ 404 Error Handling: Status {response.status_code}")
    except Exception as e:
        results["404 Handling"] = False
        print(f"❌ 404 Error Handling: Failed - {e}")
    
    # Test malformed URL handling
    try:
        response = requests.get('http://localhost:3000/api/invalid-endpoint', timeout=10)
        results["Invalid Endpoint"] = response.status_code in [200, 404, 500]
        print(f"✅ Invalid Endpoint Handling: Status {response.status_code}")
    except Exception as e:
        results["Invalid Endpoint"] = False
        print(f"❌ Invalid Endpoint Handling: Failed - {e}")
    
    return results

def test_firebase_functions_error_handling() -> Dict[str, bool]:
    """Test Firebase Functions error handling"""
    results = {}
    
    print("\nTesting Firebase Functions Error Handling...")
    
    # Test calling non-existent function
    try:
        url = "http://127.0.0.1:5002/react-app-000730-default/australia-southeast1/nonexistent_function"
        response = requests.post(url, json={"data": {}}, timeout=10)
        results["Non-existent Function"] = response.status_code == 404
        print(f"✅ Non-existent Function: Status {response.status_code}")
    except Exception as e:
        results["Non-existent Function"] = False
        print(f"❌ Non-existent Function: Failed - {e}")
    
    # Test malformed request to existing function
    try:
        url = "http://127.0.0.1:5002/react-app-000730-default/australia-southeast1/get_ai_system_status"
        response = requests.post(url, json={"invalid": "data"}, timeout=10)
        # Should handle malformed requests gracefully
        results["Malformed Request"] = response.status_code in [200, 400, 500]
        print(f"✅ Malformed Request Handling: Status {response.status_code}")
    except Exception as e:
        results["Malformed Request"] = False
        print(f"❌ Malformed Request Handling: Failed - {e}")
    
    return results

def test_emulator_error_responses() -> Dict[str, bool]:
    """Test Firebase emulator error responses"""
    results = {}
    
    print("\nTesting Firebase Emulator Error Responses...")
    
    # Test invalid Firestore request
    try:
        response = requests.get('http://127.0.0.1:8081/invalid-path', timeout=10)
        results["Firestore Invalid Path"] = response.status_code in [400, 404, 500]
        print(f"✅ Firestore Invalid Path: Status {response.status_code}")
    except Exception as e:
        results["Firestore Invalid Path"] = False
        print(f"❌ Firestore Invalid Path: Failed - {e}")
    
    # Test invalid Auth request
    try:
        response = requests.post('http://127.0.0.1:9100/invalid-auth-endpoint', 
                               json={"invalid": "data"}, timeout=10)
        results["Auth Invalid Request"] = response.status_code in [400, 404, 500]
        print(f"✅ Auth Invalid Request: Status {response.status_code}")
    except Exception as e:
        results["Auth Invalid Request"] = False
        print(f"❌ Auth Invalid Request: Failed - {e}")
    
    return results

def test_logging_mechanisms() -> Dict[str, bool]:
    """Test logging mechanisms"""
    results = {}
    
    print("\nTesting Logging Mechanisms...")
    
    # Check if log files exist or are being generated
    import os
    
    # Check for Firebase debug logs
    log_files = [
        'firestore-debug.log',
        'firebase-debug.log',
        'ui-debug.log'
    ]
    
    log_files_found = 0
    for log_file in log_files:
        if os.path.exists(f'../{log_file}'):
            log_files_found += 1
            print(f"✅ Found log file: {log_file}")
        else:
            print(f"⚠️ Log file not found: {log_file}")
    
    results["Log Files Present"] = log_files_found > 0
    
    # Test if emulator is generating logs (check if emulator process is logging)
    try:
        # Make a request that should generate logs
        response = requests.get('http://127.0.0.1:4400', timeout=5)
        results["Emulator Logging"] = response.status_code == 200
        print("✅ Emulator is responding and should be logging")
    except Exception as e:
        results["Emulator Logging"] = False
        print(f"❌ Emulator Logging Test: Failed - {e}")
    
    return results

def test_cors_error_handling() -> Dict[str, bool]:
    """Test CORS error handling"""
    results = {}
    
    print("\nTesting CORS Error Handling...")
    
    # Test CORS preflight request
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options('http://127.0.0.1:5002', headers=headers, timeout=10)
        results["CORS Preflight"] = response.status_code in [200, 204, 404]
        print(f"✅ CORS Preflight: Status {response.status_code}")
    except Exception as e:
        results["CORS Preflight"] = False
        print(f"❌ CORS Preflight: Failed - {e}")
    
    return results

def print_error_handling_report(all_results: Dict[str, Dict[str, bool]]):
    """Print comprehensive error handling report"""
    print("\n" + "=" * 70)
    print("ERROR HANDLING AND LOGGING VERIFICATION REPORT")
    print("=" * 70)
    
    total_tests = 0
    passing_tests = 0
    
    for category, results in all_results.items():
        print(f"\n📋 {category.upper()}:")
        print("-" * 40)
        
        for test, status in results.items():
            status_icon = "✅ PASS" if status else "❌ FAIL"
            print(f"  {test}: {status_icon}")
            total_tests += 1
            if status:
                passing_tests += 1
    
    print("\n" + "=" * 70)
    success_rate = (passing_tests / total_tests * 100) if total_tests > 0 else 0
    print(f"ERROR HANDLING HEALTH: {passing_tests}/{total_tests} tests ({success_rate:.1f}%)")
    
    if success_rate >= 90:
        print("🎉 ERROR HANDLING STATUS: EXCELLENT")
    elif success_rate >= 75:
        print("✅ ERROR HANDLING STATUS: GOOD")
    elif success_rate >= 60:
        print("⚠️ ERROR HANDLING STATUS: NEEDS IMPROVEMENT")
    else:
        print("🚨 ERROR HANDLING STATUS: CRITICAL")
    
    print("=" * 70)

if __name__ == "__main__":
    print("Starting Error Handling and Logging Verification...")
    print("Testing error handling mechanisms across all application components...")
    print("\n" + "-" * 50)
    
    # Run all error handling tests
    all_results = {
        "Frontend Error Handling": test_frontend_error_handling(),
        "Firebase Functions Errors": test_firebase_functions_error_handling(),
        "Emulator Error Responses": test_emulator_error_responses(),
        "Logging Mechanisms": test_logging_mechanisms(),
        "CORS Error Handling": test_cors_error_handling()
    }
    
    # Print comprehensive report
    print_error_handling_report(all_results)

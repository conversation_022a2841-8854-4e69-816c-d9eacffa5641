{"multi_stage_builds": {"development": {"optimization_level": "basic", "source_maps": true, "hot_reload": true, "debug_mode": true}, "staging": {"optimization_level": "medium", "source_maps": true, "hot_reload": false, "debug_mode": false}, "production": {"optimization_level": "maximum", "source_maps": false, "hot_reload": false, "debug_mode": false, "minification": true, "tree_shaking": true}}, "build_optimization": {"parallel_builds": true, "incremental_builds": true, "build_caching": true, "dependency_caching": true, "artifact_compression": true}, "quality_gates": {"unit_tests": {"threshold": 80, "required": true}, "integration_tests": {"threshold": 75, "required": true}, "code_coverage": {"threshold": 85, "required": true}, "linting": {"errors": 0, "required": true}, "security_scan": {"high_severity": 0, "required": true}}}
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { Analytics } from '../pages/Analytics';
import { AuthProvider } from '../contexts/AuthContext';
import { WorkspaceProvider } from '../contexts/WorkspaceContext';

// Mock Firebase
vi.mock('../config/firebase', () => ({
  auth: {},
  db: {},
  functions: {},
  storage: {}
}));

// Mock analytics service
vi.mock('../services/analyticsService', () => ({
  analyticsService: {
    getUserAnalytics: vi.fn().mockRejectedValue(new Error('Mock database error'))
  }
}));

// Mock auth context
const mockUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User'
};

vi.mock('../contexts/AuthContext', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => children,
  useAuth: () => ({
    currentUser: mockUser,
    user: mockUser,
    loading: false
  })
}));

// Mock workspace context
vi.mock('../contexts/WorkspaceContext', () => ({
  WorkspaceProvider: ({ children }: { children: React.ReactNode }) => children,
  useWorkspace: () => ({
    currentWorkspace: null,
    workspaces: [],
    loading: false
  })
}));

// Mock chart components
vi.mock('../components/analytics/Charts', () => ({
  ActivityChart: () => <div data-testid="activity-chart">Activity Chart</div>,
  ModelUsageChart: () => <div data-testid="model-usage-chart">Model Usage Chart</div>,
  CostBreakdownChart: () => <div data-testid="cost-breakdown-chart">Cost Breakdown Chart</div>,
  PerformanceChart: () => <div data-testid="performance-chart">Performance Chart</div>,
  MetricCard: ({ title, value }: { title: string; value: string | number }) => (
    <div data-testid={`metric-card-${title.toLowerCase().replace(/\s+/g, '-')}`}>
      {title}: {value}
    </div>
  ),
  TopPromptsTable: () => <div data-testid="top-prompts-table">Top Prompts Table</div>
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <AuthProvider>
      <WorkspaceProvider>
        {children}
      </WorkspaceProvider>
    </AuthProvider>
  </BrowserRouter>
);

describe('Analytics Page', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state initially', () => {
    render(
      <TestWrapper>
        <Analytics />
      </TestWrapper>
    );

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders mock data in development mode when database fails', async () => {
    // Set development mode
    vi.stubEnv('DEV', true);

    render(
      <TestWrapper>
        <Analytics />
      </TestWrapper>
    );

    // Wait for loading to complete and mock data to load
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    // Check that development mode banner is shown
    expect(screen.getByText('Development Mode')).toBeInTheDocument();
    expect(screen.getByText(/You're viewing mock analytics data/)).toBeInTheDocument();

    // Check that analytics content is rendered
    expect(screen.getByText('Analytics Overview')).toBeInTheDocument();
    expect(screen.getByTestId('activity-chart')).toBeInTheDocument();
    expect(screen.getByTestId('model-usage-chart')).toBeInTheDocument();
  });

  it('handles authentication state correctly', async () => {
    render(
      <TestWrapper>
        <Analytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    // Should render analytics content when user is authenticated
    expect(screen.getByText('Analytics Overview')).toBeInTheDocument();
  });

  it('displays metric cards with mock data', async () => {
    vi.stubEnv('DEV', true);

    render(
      <TestWrapper>
        <Analytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    // Check for metric cards (they should be rendered with mock data)
    expect(screen.getByTestId('metric-card-total-prompts')).toBeInTheDocument();
    expect(screen.getByTestId('metric-card-total-executions')).toBeInTheDocument();
    expect(screen.getByTestId('metric-card-success-rate')).toBeInTheDocument();
  });

  it('renders tab navigation correctly', async () => {
    vi.stubEnv('DEV', true);

    render(
      <TestWrapper>
        <Analytics />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    // Check for tab navigation
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Real-time')).toBeInTheDocument();
    expect(screen.getByText('A/B Tests')).toBeInTheDocument();
    expect(screen.getByText('Cost Optimization')).toBeInTheDocument();
  });
});

// Compatibility shim for react-is to work with Recharts
// This provides the exports that Recharts expects

// Import the actual react-is module
import * as ReactIs from 'react-is';

// Re-export all available exports
export * from 'react-is';

// Ensure specific exports that Recharts needs are available
export const isFragment = ReactIs.isFragment || (() => false);
export const isValidElement = ReactIs.isValidElement || (() => false);
export const isElement = ReactIs.isElement || (() => false);
export const isValidElementType = ReactIs.isValidElementType || (() => false);
export const typeOf = ReactIs.typeOf || (() => null);

// Export constants that might be missing
export const Fragment = ReactIs.Fragment || Symbol.for('react.fragment');
export const Element = ReactIs.Element || Symbol.for('react.element');
export const ForwardRef = ReactIs.ForwardRef || Symbol.for('react.forward_ref');
export const Memo = ReactIs.Memo || Symbol.for('react.memo');
export const Lazy = ReactIs.Lazy || Symbol.for('react.lazy');
export const Suspense = ReactIs.Suspense || Symbol.for('react.suspense');
export const Profiler = ReactIs.Profiler || Symbol.for('react.profiler');
export const StrictMode = ReactIs.StrictMode || Symbol.for('react.strict_mode');
export const Portal = ReactIs.Portal || Symbol.for('react.portal');
export const ContextProvider = ReactIs.ContextProvider || Symbol.for('react.provider');
export const ContextConsumer = ReactIs.ContextConsumer || Symbol.for('react.context');

// Export everything as default as well for compatibility
export default ReactIs;

firebase-functions>=0.1.0
firebase-admin>=6.0.0
openai>=1.0.0
anthropic>=0.25.0
google-generativeai>=0.3.0
google-cloud-aiplatform>=1.38.0
pinecone-client>=3.0.0
redis>=5.0.0
requests>=2.31.0
python-dotenv>=1.0.0
pydantic>=2.0.0
tiktoken>=0.5.0
numpy>=1.24.0
scikit-learn>=1.3.0
sentence-transformers>=2.2.0
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-anthropic>=0.1.0
langchain-google-genai>=0.1.0
langchain-pinecone>=0.1.0
flask>=2.3.0
flask-cors>=4.0.0
fastapi>=0.104.0
uvicorn>=0.24.0
python-multipart>=0.0.6
aiohttp>=3.8.0
asyncio-throttle>=1.0.0
PyPDF2>=3.0.0
python-docx>=0.8.11
markdown>=3.4.0
beautifulsoup4>=4.12.0
chardet>=5.0.0
python-magic>=0.4.27

# Testing dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
httpx>=0.24.0

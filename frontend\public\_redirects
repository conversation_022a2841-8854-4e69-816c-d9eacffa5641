# Netlify redirects and caching configuration

# SPA fallback - serve index.html for all routes
/*    /index.html   200

# API proxy (if needed)
/api/*  https://your-api-domain.com/api/:splat  200

# Cache static assets with long expiration
/assets/*  /assets/:splat  200!  Cache-Control: public,max-age=31536000,immutable

# Cache fonts with long expiration
/fonts/*   /fonts/:splat   200!  Cache-Control: public,max-age=31536000,immutable

# Cache images with medium expiration
/images/*  /images/:splat  200!  Cache-Control: public,max-age=2592000

# Service worker - no cache
/sw.js     /sw.js          200!  Cache-Control: no-cache,no-store,must-revalidate

# Manifest - short cache
/manifest.json  /manifest.json  200!  Cache-Control: public,max-age=86400

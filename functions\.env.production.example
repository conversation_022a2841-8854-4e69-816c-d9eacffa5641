# Production Environment Configuration
# Copy this file to .env.production and fill in your actual API keys
# NEVER commit the actual .env.production file to version control

# Google AI Platform API Key (Primary embedding provider)
# Get from: https://console.cloud.google.com/apis/credentials
GOOGLE_API_KEY=AIza...your-google-api-key-here

# OpenRouter API Key (Fallback embedding provider)
# Get from: https://openrouter.ai/keys
OPENROUTER_API_KEY=sk-or-v1...your-openrouter-api-key-here

# OpenAI API Key (Optional additional fallback)
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-...your-openai-api-key-here

# Production Site URL
PRODUCTION_SITE_URL=https://your-app.web.app

# Environment Configuration
ENVIRONMENT_MODE=production
PYTHON_ENV=production
DEBUG=false

# Firebase Project Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_REGION=australia-southeast1

# API Configuration
API_RATE_LIMIT_PER_MINUTE=100
API_RATE_LIMIT_PER_HOUR=1000
API_RATE_LIMIT_PER_DAY=10000

# Embedding Configuration
DEFAULT_EMBEDDING_PROVIDER=google
FALLBACK_EMBEDDING_PROVIDER=openrouter
EMBEDDING_DIMENSIONS=768
MAX_EMBEDDING_BATCH_SIZE=100

# Cache Configuration
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE_MB=512

# Monitoring Configuration
HEALTH_CHECK_TIMEOUT_SECONDS=30
METRICS_COLLECTION_ENABLED=true
ALERT_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...

# Security Configuration
CORS_ALLOWED_ORIGINS=https://your-app.web.app,https://your-domain.com
MAX_REQUEST_SIZE_MB=10
REQUEST_TIMEOUT_SECONDS=300

# Performance Configuration
MAX_CONCURRENT_REQUESTS=100
FUNCTION_MEMORY_MB=2048
FUNCTION_TIMEOUT_SECONDS=540

# Cost Optimization
GOOGLE_API_QUOTA_LIMIT_PER_DAY=1000000
OPENROUTER_BUDGET_LIMIT_USD=100
COST_ALERT_THRESHOLD_USD=80

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_RETENTION_DAYS=30

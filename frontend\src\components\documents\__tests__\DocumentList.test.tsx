import '@testing-library/jest-dom';
import React from 'react';
import { render, screen, cleanup } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { DocumentList } from '../DocumentList';

// Mock useAuth
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn(() => ({
    currentUser: { uid: 'test-user-id' }
  }))
}));

// Mock DocumentService
vi.mock('../../../services/documentService', () => ({
  DocumentService: {
    subscribeToUserDocuments: vi.fn(() => () => {}), // Return unsubscribe function
    deleteDocument: vi.fn(() => Promise.resolve()),
    formatFileSize: vi.fn((size) => `${size} bytes`),
    getFileTypeIcon: vi.fn(() => '📄')
  }
}));

// Mock Toast context
vi.mock('../../common/Toast', () => ({
  useToast: () => ({
    showToast: vi.fn()
  })
}));

afterEach(() => {
    cleanup();
  });

  describe('DocumentList', () => {
  it('renders without crashing', () => {
    render(<DocumentList />);
    // Component shows loading spinner initially
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });
});

# Staging Environment Configuration
# RAG Prompt Library - Staging Settings

# Firebase Configuration - Staging
VITE_FIREBASE_API_KEY=your_staging_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=rag-prompt-library-staging.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=rag-prompt-library-staging
VITE_FIREBASE_STORAGE_BUCKET=rag-prompt-library-staging.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=your_staging_sender_id
VITE_FIREBASE_APP_ID=your_staging_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_staging_measurement_id

# Staging Environment Settings
VITE_USE_EMULATORS=false
VITE_APP_ENVIRONMENT=staging

# Application Configuration
VITE_APP_NAME=RAG Prompt Library (Staging)
VITE_APP_VERSION=1.0.0-staging
VITE_APP_DESCRIPTION=Smart, Modular, RAG-Enabled Prompt Library System (Staging)

# Feature Flags - Staging
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_DEBUG_TOOLS=true

# API Configuration - Staging
# VITE_OPENROUTER_API_KEY will be injected during deployment

# Performance Settings - Staging
VITE_ENABLE_SERVICE_WORKER=true
VITE_ENABLE_OFFLINE_SUPPORT=true
VITE_CACHE_STRATEGY=stale-while-revalidate

# Security Settings - Staging
VITE_ENABLE_CSP=true
VITE_ENABLE_HTTPS_ONLY=true

# Monitoring and Logging - Staging
VITE_LOG_LEVEL=info
VITE_DEBUG_MODE=false
VITE_ENABLE_CONSOLE_LOGS=true

# UI/UX Settings - Staging
VITE_THEME_MODE=auto
VITE_DEFAULT_LANGUAGE=en
VITE_ENABLE_ANIMATIONS=true

# API Endpoints - Staging
VITE_API_BASE_URL=https://australia-southeast1-rag-prompt-library-staging.cloudfunctions.net
VITE_FUNCTIONS_REGION=australia-southeast1

# Rate Limiting - Staging (more lenient than production)
VITE_API_RATE_LIMIT=200
VITE_API_RATE_WINDOW=60000

# File Upload Limits - Staging
VITE_MAX_FILE_SIZE=20971520
VITE_ALLOWED_FILE_TYPES=pdf,txt,doc,docx,md,json

# Feature Limits - Staging (higher than production for testing)
VITE_MAX_PROMPTS_PER_USER=2000
VITE_MAX_DOCUMENTS_PER_USER=200
VITE_MAX_EXECUTIONS_PER_DAY=1000

# Development Tools - Staging
VITE_ENABLE_REACT_DEVTOOLS=true
VITE_ENABLE_REDUX_DEVTOOLS=false
VITE_ENABLE_HOT_RELOAD=false

# Netlify headers configuration
# https://docs.netlify.com/routing/headers/

# Cache static assets for 1 year
/assets/*
  Cache-Control: public, max-age=********, immutable
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block

# Cache fonts for 1 year
/fonts/*
  Cache-Control: public, max-age=********, immutable
  X-Content-Type-Options: nosniff

# Cache images for 1 month
/images/*
  Cache-Control: public, max-age=2592000
  X-Content-Type-Options: nosniff

# Cache JavaScript and CSS for 1 year (with hash in filename)
*.js
  Cache-Control: public, max-age=********, immutable
  X-Content-Type-Options: nosniff

*.css
  Cache-Control: public, max-age=********, immutable
  X-Content-Type-Options: nosniff

# Cache compressed files
*.gz
  Content-Encoding: gzip
  Cache-Control: public, max-age=********, immutable

*.br
  Content-Encoding: br
  Cache-Control: public, max-age=********, immutable

# HTML files - short cache with revalidation
*.html
  Cache-Control: public, max-age=0, must-revalidate
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin

# Service worker - no cache
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  X-Content-Type-Options: nosniff

# Manifest - short cache
/manifest.json
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff

# API routes - no cache by default
/api/*
  Cache-Control: no-cache, no-store, must-revalidate
  X-Content-Type-Options: nosniff

# Root and main pages
/
  Cache-Control: public, max-age=0, must-revalidate
  X-Content-Type-Options: nosniff
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://*.firebaseapp.com https://*.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://*.firebaseapp.com https://*.googleapis.com wss://*.firebaseapp.com; frame-src 'self' https://accounts.google.com;

#!/usr/bin/env python3
"""
Configuration Validation Script
Validates all environment variables, Firebase config, and configuration files
"""
import os
import json
import yaml
from typing import Dict, List, Any

def validate_environment_variables() -> Dict[str, bool]:
    """Validate required environment variables"""
    results = {}
    
    print("Validating Environment Variables...")
    
    # Required environment variables for production
    required_env_vars = [
        'GOOGLE_API_KEY',        # For Google embeddings (primary)
        'OPENROUTER_API_KEY',    # For LLM access and OpenAI embeddings fallback
        'COHERE_API_KEY',        # For Cohere LLM and reranking
        'ANTHROPIC_API_KEY',     # For Anthropic LLM
        'PINECONE_API_KEY'       # For vector database
    ]
    
    # Optional but recommended environment variables
    optional_env_vars = [
        'REDIS_URL',
        'DATABASE_URL',
        'FIREBASE_PROJECT_ID',
        'GOOGLE_APPLICATION_CREDENTIALS'
    ]
    
    print("\n📋 Required API Keys:")
    for var in required_env_vars:
        value = os.getenv(var)
        if value:
            results[f"Required: {var}"] = True
            print(f"  ✅ {var}: Set (length: {len(value)})")
        else:
            results[f"Required: {var}"] = False
            print(f"  ❌ {var}: Not set")
    
    print("\n📋 Optional Configuration:")
    for var in optional_env_vars:
        value = os.getenv(var)
        if value:
            results[f"Optional: {var}"] = True
            print(f"  ✅ {var}: Set")
        else:
            results[f"Optional: {var}"] = False
            print(f"  ⚠️ {var}: Not set")
    
    return results

def validate_firebase_config() -> Dict[str, bool]:
    """Validate Firebase configuration files"""
    results = {}
    
    print("\nValidating Firebase Configuration...")
    
    # Check firebase.json
    try:
        with open('../firebase.json', 'r') as f:
            firebase_config = json.load(f)
        
        # Validate hosting configuration
        if 'hosting' in firebase_config:
            hosting = firebase_config['hosting']
            results["Firebase Hosting Config"] = all([
                'public' in hosting,
                'rewrites' in hosting,
                'headers' in hosting
            ])
            print(f"  ✅ Firebase Hosting: Configured")
        else:
            results["Firebase Hosting Config"] = False
            print(f"  ❌ Firebase Hosting: Not configured")
        
        # Validate functions configuration
        if 'functions' in firebase_config:
            functions = firebase_config['functions']
            if isinstance(functions, list) and len(functions) > 0:
                func_config = functions[0]
                results["Firebase Functions Config"] = all([
                    'source' in func_config,
                    'runtime' in func_config
                ])
                print(f"  ✅ Firebase Functions: Configured")
            else:
                results["Firebase Functions Config"] = False
                print(f"  ❌ Firebase Functions: Not properly configured")
        else:
            results["Firebase Functions Config"] = False
            print(f"  ❌ Firebase Functions: Not configured")
        
        # Validate Firestore configuration
        if 'firestore' in firebase_config:
            results["Firebase Firestore Config"] = True
            print(f"  ✅ Firebase Firestore: Configured")
        else:
            results["Firebase Firestore Config"] = False
            print(f"  ❌ Firebase Firestore: Not configured")
        
        # Validate emulators configuration
        if 'emulators' in firebase_config:
            emulators = firebase_config['emulators']
            required_emulators = ['auth', 'functions', 'firestore', 'hosting', 'storage']
            has_all_emulators = all(emu in emulators for emu in required_emulators)
            results["Firebase Emulators Config"] = has_all_emulators
            print(f"  ✅ Firebase Emulators: Configured")
        else:
            results["Firebase Emulators Config"] = False
            print(f"  ❌ Firebase Emulators: Not configured")
            
    except FileNotFoundError:
        results["Firebase Config File"] = False
        print(f"  ❌ firebase.json: File not found")
    except json.JSONDecodeError:
        results["Firebase Config File"] = False
        print(f"  ❌ firebase.json: Invalid JSON")
    
    return results

def validate_frontend_config() -> Dict[str, bool]:
    """Validate frontend configuration files"""
    results = {}
    
    print("\nValidating Frontend Configuration...")
    
    # Check package.json
    try:
        with open('../frontend/package.json', 'r') as f:
            package_config = json.load(f)
        
        # Validate scripts
        required_scripts = ['dev', 'build', 'test', 'preview']
        scripts = package_config.get('scripts', {})
        has_required_scripts = all(script in scripts for script in required_scripts)
        results["Frontend Scripts"] = has_required_scripts
        print(f"  ✅ Frontend Scripts: {'All present' if has_required_scripts else 'Missing some'}")
        
        # Validate dependencies
        deps = package_config.get('dependencies', {})
        required_deps = ['react', 'react-dom', 'firebase']
        has_required_deps = all(dep in deps for dep in required_deps)
        results["Frontend Dependencies"] = has_required_deps
        print(f"  ✅ Frontend Dependencies: {'All present' if has_required_deps else 'Missing some'}")
        
    except FileNotFoundError:
        results["Frontend Package Config"] = False
        print(f"  ❌ frontend/package.json: File not found")
    except json.JSONDecodeError:
        results["Frontend Package Config"] = False
        print(f"  ❌ frontend/package.json: Invalid JSON")
    
    # Check Vite config
    vite_config_files = ['../frontend/vite.config.ts', '../frontend/vite.config.js']
    vite_config_exists = any(os.path.exists(f) for f in vite_config_files)
    results["Vite Config"] = vite_config_exists
    print(f"  {'✅' if vite_config_exists else '❌'} Vite Config: {'Found' if vite_config_exists else 'Not found'}")
    
    # Check TypeScript config
    ts_config_exists = os.path.exists('../frontend/tsconfig.json')
    results["TypeScript Config"] = ts_config_exists
    print(f"  {'✅' if ts_config_exists else '❌'} TypeScript Config: {'Found' if ts_config_exists else 'Not found'}")
    
    return results

def validate_backend_config() -> Dict[str, bool]:
    """Validate backend configuration files"""
    results = {}
    
    print("\nValidating Backend Configuration...")
    
    # Check requirements.txt
    requirements_exists = os.path.exists('requirements.txt')
    results["Requirements File"] = requirements_exists
    print(f"  {'✅' if requirements_exists else '❌'} requirements.txt: {'Found' if requirements_exists else 'Not found'}")
    
    if requirements_exists:
        try:
            with open('requirements.txt', 'r') as f:
                requirements = f.read()
            
            # Check for essential packages
            essential_packages = ['firebase-functions', 'firebase-admin', 'openai', 'anthropic']
            has_essential = all(pkg in requirements for pkg in essential_packages)
            results["Essential Packages"] = has_essential
            print(f"  ✅ Essential Packages: {'All present' if has_essential else 'Missing some'}")
            
        except Exception as e:
            results["Requirements Content"] = False
            print(f"  ❌ Requirements Content: Error reading - {e}")
    
    # Check pytest configuration
    pytest_config_exists = os.path.exists('pytest.ini')
    results["Pytest Config"] = pytest_config_exists
    print(f"  {'✅' if pytest_config_exists else '❌'} pytest.ini: {'Found' if pytest_config_exists else 'Not found'}")
    
    return results

def validate_security_config() -> Dict[str, bool]:
    """Validate security configuration"""
    results = {}
    
    print("\nValidating Security Configuration...")
    
    # Check if .env files are properly ignored
    gitignore_exists = os.path.exists('../.gitignore')
    if gitignore_exists:
        try:
            with open('../.gitignore', 'r') as f:
                gitignore_content = f.read()
            
            security_patterns = ['.env', '*.key', 'credentials.json', 'service-account']
            has_security_patterns = any(pattern in gitignore_content for pattern in security_patterns)
            results["Security Patterns in .gitignore"] = has_security_patterns
            print(f"  ✅ Security Patterns: {'Protected' if has_security_patterns else 'Not protected'}")
            
        except Exception as e:
            results["Gitignore Security"] = False
            print(f"  ❌ .gitignore Security: Error reading - {e}")
    else:
        results["Gitignore Exists"] = False
        print(f"  ❌ .gitignore: File not found")
    
    return results

def print_configuration_report(all_results: Dict[str, Dict[str, bool]]):
    """Print comprehensive configuration report"""
    print("\n" + "=" * 70)
    print("CONFIGURATION VALIDATION REPORT")
    print("=" * 70)
    
    total_configs = 0
    valid_configs = 0
    
    for category, results in all_results.items():
        print(f"\n📋 {category.upper()}:")
        print("-" * 40)
        
        for config, status in results.items():
            status_icon = "✅ VALID" if status else "❌ INVALID"
            print(f"  {config}: {status_icon}")
            total_configs += 1
            if status:
                valid_configs += 1
    
    print("\n" + "=" * 70)
    config_health = (valid_configs / total_configs * 100) if total_configs > 0 else 0
    print(f"CONFIGURATION HEALTH: {valid_configs}/{total_configs} configs ({config_health:.1f}%)")
    
    if config_health >= 90:
        print("🎉 CONFIGURATION STATUS: EXCELLENT")
    elif config_health >= 75:
        print("✅ CONFIGURATION STATUS: GOOD")
    elif config_health >= 60:
        print("⚠️ CONFIGURATION STATUS: NEEDS ATTENTION")
    else:
        print("🚨 CONFIGURATION STATUS: CRITICAL")
    
    print("=" * 70)

if __name__ == "__main__":
    print("Starting Configuration Validation...")
    print("Validating all environment variables, Firebase config, and configuration files...")
    print("\n" + "-" * 50)
    
    # Run all configuration validation tests
    all_results = {
        "Environment Variables": validate_environment_variables(),
        "Firebase Configuration": validate_firebase_config(),
        "Frontend Configuration": validate_frontend_config(),
        "Backend Configuration": validate_backend_config(),
        "Security Configuration": validate_security_config()
    }
    
    # Print comprehensive report
    print_configuration_report(all_results)
